class Exhibition {
  final int id;
  final String name;
  final String? description;
  final String? address;
  final String? manager;
  final String? phone;
  final String? email;
  final bool isActive;
  final DateTime? startDate;
  final DateTime? endDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  Exhibition({
    required this.id,
    required this.name,
    this.description,
    this.address,
    this.manager,
    this.phone,
    this.email,
    this.isActive = true,
    this.startDate,
    this.endDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Exhibition.fromJson(Map<String, dynamic> json) {
    return Exhibition(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      address: json['address'] as String?,
      manager: json['manager'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      startDate: json['start_date'] != null
          ? DateTime.parse(json['start_date'] as String)
          : null,
      endDate: json['end_date'] != null
          ? DateTime.parse(json['end_date'] as String)
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'manager': manager,
      'phone': phone,
      'email': email,
      'is_active': isActive,
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Exhibition copyWith({
    int? id,
    String? name,
    String? description,
    String? address,
    String? manager,
    String? phone,
    String? email,
    bool? isActive,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Exhibition(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      manager: manager ?? this.manager,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      isActive: isActive ?? this.isActive,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Exhibition && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Exhibition(id: $id, name: $name)';

  // Helper methods
  String get statusText => isActive ? 'فعال' : 'غیرفعال';
  
  bool get isUpcoming {
    if (startDate == null) return false;
    return DateTime.now().isBefore(startDate!);
  }
  
  bool get isOngoing {
    if (startDate == null) return isActive;
    if (endDate == null) return DateTime.now().isAfter(startDate!) && isActive;
    return DateTime.now().isAfter(startDate!) && 
           DateTime.now().isBefore(endDate!) && 
           isActive;
  }
  
  bool get isEnded {
    if (endDate == null) return false;
    return DateTime.now().isAfter(endDate!);
  }
  
  String get durationText {
    if (startDate == null) return 'نامشخص';
    if (endDate == null) return 'از ${_formatDate(startDate!)}';
    return 'از ${_formatDate(startDate!)} تا ${_formatDate(endDate!)}';
  }
  
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }
}
