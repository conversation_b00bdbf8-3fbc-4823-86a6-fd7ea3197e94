import 'package:sangin_foroosh/core/utils/optional.dart';

class InvoiceItem {
  final int? id;
  final int? invoiceId;
  final int? productId;
  final String description;
  final int quantity;
  final double unitPrice;
  final double discountAmount;
  final double taxAmount;
  final double totalAmount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? product;

  const InvoiceItem({
    this.id,
    this.invoiceId,
    this.productId,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    this.discountAmount = 0.0,
    this.taxAmount = 0.0,
    required this.totalAmount,
    required this.createdAt,
    required this.updatedAt,
    this.product,
  });

  factory InvoiceItem.fromJson(Map<String, dynamic> json) {
    return InvoiceItem(
      id: json['id'] as int?,
      invoiceId: json['invoice_id'] as int?,
      productId: json['product_id'] as int?,
      description: json['description'] as String,
      quantity: json['quantity'] as int,
      unitPrice: json['unit_price'] != null
          ? double.parse(json['unit_price'].toString())
          : 0.0,
      discountAmount: json['discount_amount'] != null
          ? double.parse(json['discount_amount'].toString())
          : 0.0,
      taxAmount: json['tax_amount'] != null
          ? double.parse(json['tax_amount'].toString())
          : 0.0,
      totalAmount: json['total_amount'] != null
          ? double.parse(json['total_amount'].toString())
          : 0.0,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
      product: json['product'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'invoice_id': invoiceId,
      'product_id': productId,
      'description': description,
      'quantity': quantity,
      'unit_price': unitPrice,
      'discount_amount': discountAmount,
      'tax_amount': taxAmount,
      'total_amount': totalAmount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  InvoiceItem copyWith({
    int? id,
    Optional<int?>? invoiceId,
    Optional<int?>? productId,
    String? description,
    int? quantity,
    double? unitPrice,
    double? discountAmount,
    double? taxAmount,
    double? totalAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
    Optional<Map<String, dynamic>?>? product,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      invoiceId: invoiceId == null ? this.invoiceId : invoiceId.value,
      productId: productId == null ? this.productId : productId.value,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      discountAmount: discountAmount ?? this.discountAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      product: product == null ? this.product : product.value,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'InvoiceItem(id: $id, description: $description, quantity: $quantity)';

  // Helper methods
  String get formattedUnitPrice => '${unitPrice.toStringAsFixed(0)} تومان';
  String get formattedDiscountAmount => '${discountAmount.toStringAsFixed(0)} تومان';
  String get formattedTaxAmount => '${taxAmount.toStringAsFixed(0)} تومان';
  String get formattedTotalAmount => '${totalAmount.toStringAsFixed(0)} تومان';
  
  double get subtotal => unitPrice * quantity;
  String get formattedSubtotal => '${subtotal.toStringAsFixed(0)} تومان';
  
  String get productName {
    if (product != null && product!.containsKey('name')) {
      return product!['name'] as String;
    }
    return description;
  }
  
  String? get productSku {
    if (product != null && product!.containsKey('sku')) {
      return product!['sku'] as String;
    }
    return null;
  }
}