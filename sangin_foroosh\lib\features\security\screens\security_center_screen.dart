import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/security_provider.dart';
import '../models/permission.dart';
import '../models/role.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/loading_widget.dart';

class SecurityCenterScreen extends StatefulWidget {
  static const String routeName = '/security-center';
  
  const SecurityCenterScreen({super.key});

  @override
  State<SecurityCenterScreen> createState() => _SecurityCenterScreenState();
}

class _SecurityCenterScreenState extends State<SecurityCenterScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isInit = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInit) {
      _loadData();
      _isInit = true;
    }
  }

  Future<void> _loadData() async {
    if (!mounted) return;
    await Provider.of<SecurityProvider>(context, listen: false).loadInitialData();
  }

  Future<void> _refreshData() async {
    if (!mounted) return;
    await Provider.of<SecurityProvider>(context, listen: false).refreshData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مرکز امنیت'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'نقش‌های من'),
            Tab(text: 'دسترسی‌های من'),
          ],
        ),
      ),
      body: Consumer<SecurityProvider>(
        builder: (ctx, securityProvider, child) {
          if (securityProvider.isLoading) {
            return const LoadingWidget();
          }

          if (securityProvider.error != null) {
            return AppErrorWidget(
              message: securityProvider.error!,
              onRetry: _refreshData,
            );
          }

          return RefreshIndicator(
            onRefresh: _refreshData,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildRolesTab(securityProvider),
                _buildPermissionsTab(securityProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildRolesTab(SecurityProvider securityProvider) {
    final userRoles = securityProvider.userRoles;

    if (userRoles.isEmpty) {
      return const Center(
        child: Text('هیچ نقشی برای شما تعریف نشده است.'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: userRoles.length,
      itemBuilder: (ctx, index) {
        final role = userRoles[index];
        return _buildRoleCard(role);
      },
    );
  }

  Widget _buildRoleCard(Role role) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        title: Text(
          role.displayNameOrName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: role.description != null
            ? Text(
                role.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              )
            : null,
        children: [
          if (role.permissions.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'دسترسی‌های این نقش:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: role.permissions
                        .map((permission) => Chip(
                              label: Text(permission),
                              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                            ))
                        .toList(),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPermissionsTab(SecurityProvider securityProvider) {
    final permissionGroups = securityProvider.permissionGroups;

    if (permissionGroups.isEmpty) {
      return const Center(
        child: Text('هیچ دسترسی برای شما تعریف نشده است.'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: permissionGroups.length,
      itemBuilder: (ctx, index) {
        final groupName = permissionGroups.keys.elementAt(index);
        final permissions = permissionGroups[groupName]!;
        return _buildPermissionGroupCard(groupName, permissions);
      },
    );
  }

  Widget _buildPermissionGroupCard(String groupName, List<Permission> permissions) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        title: Text(
          groupName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('${permissions.length} دسترسی'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: permissions
                  .map((permission) => ListTile(
                        title: Text(permission.displayNameOrName),
                        subtitle: permission.description != null
                            ? Text(
                                permission.description!,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              )
                            : null,
                        leading: const Icon(Icons.security),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }
}