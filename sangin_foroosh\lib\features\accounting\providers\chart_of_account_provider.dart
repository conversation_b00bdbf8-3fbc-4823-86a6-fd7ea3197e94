import 'package:flutter/material.dart';
import '../models/models.dart';
import '../repositories/accounting_repository.dart';

class ChartOfAccountProvider extends ChangeNotifier {
  final AccountingRepository _repository;

  ChartOfAccountProvider() : _repository = AccountingRepository();

  List<ChartOfAccount> _chartOfAccounts = [];
  ChartOfAccount? _selectedChartOfAccount;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _pagination = {};
  List<ChartOfAccount> _treeStructure = [];

  // Getters
  List<ChartOfAccount> get chartOfAccounts => _chartOfAccounts;
  List<ChartOfAccount> get treeStructure => _treeStructure;
  ChartOfAccount? get selectedChartOfAccount => _selectedChartOfAccount;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic> get pagination => _pagination;
  bool get hasError => _error != null;
  bool get isEmpty => _chartOfAccounts.isEmpty && !_isLoading;

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error state
  void _setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  // Load chart of accounts
  Future<void> loadChartOfAccounts({
    int page = 1,
    int perPage = 20,
    String? search,
    int? accountStructureId,
    String? type,
    int? parentId,
    bool? isActive,
    bool refresh = false,
  }) async {
    try {
      if (refresh || page == 1) {
        _setLoading(true);
        _error = null;
      }

      final result = await _repository.getChartOfAccounts(
        page: page,
        perPage: perPage,
        search: search,
        accountStructureId: accountStructureId,
        type: type,
        parentId: parentId,
        isActive: isActive,
      );

      if (page == 1 || refresh) {
        _chartOfAccounts = result['accounts'];
      } else {
        _chartOfAccounts.addAll(result['accounts']);
      }
      
      _pagination = result['pagination'];
      _buildTreeStructure();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری دفتر کل حساب‌ها: ${e.toString()}');
    }
  }

  // Load chart of account by ID
  Future<void> loadChartOfAccount(int id) async {
    try {
      _setLoading(true);
      _error = null;

      _selectedChartOfAccount = await _repository.getChartOfAccount(id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری حساب: ${e.toString()}');
    }
  }

  // Create chart of account
  Future<bool> createChartOfAccount(Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final newAccount = await _repository.createChartOfAccount(data);
      _chartOfAccounts.insert(0, newAccount);
      _selectedChartOfAccount = newAccount;
      
      _buildTreeStructure();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ایجاد حساب: ${e.toString()}');
      return false;
    }
  }

  // Update chart of account
  Future<bool> updateChartOfAccount(int id, Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final updatedAccount = await _repository.updateChartOfAccount(id, data);
      
      final index = _chartOfAccounts.indexWhere((a) => a.id == id);
      if (index != -1) {
        _chartOfAccounts[index] = updatedAccount;
      }
      
      if (_selectedChartOfAccount?.id == id) {
        _selectedChartOfAccount = updatedAccount;
      }
      
      _buildTreeStructure();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ویرایش حساب: ${e.toString()}');
      return false;
    }
  }

  // Delete chart of account
  Future<bool> deleteChartOfAccount(int id) async {
    try {
      _setLoading(true);
      _error = null;

      await _repository.deleteChartOfAccount(id);
      
      _chartOfAccounts.removeWhere((a) => a.id == id);
      
      if (_selectedChartOfAccount?.id == id) {
        _selectedChartOfAccount = null;
      }
      
      _buildTreeStructure();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در حذف حساب: ${e.toString()}');
      return false;
    }
  }

  // Build tree structure from flat list
  void _buildTreeStructure() {
    _treeStructure = [];
    
    // Get root accounts (accounts with no parent)
    final rootAccounts = _chartOfAccounts.where((account) => account.parentId == null).toList();
    
    for (final rootAccount in rootAccounts) {
      _treeStructure.add(_buildAccountTree(rootAccount));
    }
  }

  // Build account tree recursively
  ChartOfAccount _buildAccountTree(ChartOfAccount account) {
    final children = _chartOfAccounts
        .where((child) => child.parentId == account.id)
        .map((child) => _buildAccountTree(child))
        .toList();
    
    return account.copyWith(children: children);
  }

  // Get accounts by type
  List<ChartOfAccount> getAccountsByType(String type) {
    return _chartOfAccounts.where((account) => account.type == type).toList();
  }

  // Get active accounts
  List<ChartOfAccount> getActiveAccounts() {
    return _chartOfAccounts.where((account) => account.isActive).toList();
  }

  // Get parent accounts (accounts that can have children)
  List<ChartOfAccount> getParentAccounts() {
    return _chartOfAccounts.where((account) => account.hasChildren).toList();
  }

  // Get child accounts of a parent
  List<ChartOfAccount> getChildAccounts(int parentId) {
    return _chartOfAccounts.where((account) => account.parentId == parentId).toList();
  }

  // Select chart of account
  void selectChartOfAccount(ChartOfAccount? account) {
    _selectedChartOfAccount = account;
    notifyListeners();
  }

  // Clear selected chart of account
  void clearSelection() {
    _selectedChartOfAccount = null;
    notifyListeners();
  }

  // Search chart of accounts
  Future<void> searchChartOfAccounts(String query) async {
    await loadChartOfAccounts(search: query, refresh: true);
  }

  // Filter by account structure
  Future<void> filterByAccountStructure(int accountStructureId) async {
    await loadChartOfAccounts(accountStructureId: accountStructureId, refresh: true);
  }

  // Filter by type
  Future<void> filterByType(String type) async {
    await loadChartOfAccounts(type: type, refresh: true);
  }

  // Filter by parent
  Future<void> filterByParent(int parentId) async {
    await loadChartOfAccounts(parentId: parentId, refresh: true);
  }

  // Filter by active status
  Future<void> filterByActiveStatus(bool isActive) async {
    await loadChartOfAccounts(isActive: isActive, refresh: true);
  }

  // Refresh chart of accounts
  Future<void> refresh() async {
    await loadChartOfAccounts(refresh: true);
  }

  // Load more chart of accounts (pagination)
  Future<void> loadMore() async {
    if (_isLoading) return;
    
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    
    if (currentPage < lastPage) {
      await loadChartOfAccounts(page: currentPage + 1);
    }
  }

  // Check if can load more
  bool get canLoadMore {
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    return currentPage < lastPage && !_isLoading;
  }

  // Get chart of account by ID from loaded list
  ChartOfAccount? getChartOfAccountById(int id) {
    try {
      return _chartOfAccounts.firstWhere((a) => a.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get chart of account by code
  ChartOfAccount? getChartOfAccountByCode(String code) {
    try {
      return _chartOfAccounts.firstWhere((a) => a.code == code);
    } catch (e) {
      return null;
    }
  }

  // Get account path (breadcrumb)
  List<ChartOfAccount> getAccountPath(ChartOfAccount account) {
    final path = <ChartOfAccount>[];
    ChartOfAccount? current = account;
    
    while (current != null) {
      path.insert(0, current);
      current = current.parentId != null ? getChartOfAccountById(current.parentId!) : null;
    }
    
    return path;
  }


}