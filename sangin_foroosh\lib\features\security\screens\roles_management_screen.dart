import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/security_provider.dart';
import '../models/role.dart';
import '../widgets/permission_gate.dart';
import 'role_details_screen.dart';

class RolesManagementScreen extends StatefulWidget {
  static const String routeName = '/roles-management';
  
  const RolesManagementScreen({super.key});

  @override
  State<RolesManagementScreen> createState() => _RolesManagementScreenState();
}

class _RolesManagementScreenState extends State<RolesManagementScreen> {
  bool _isInit = false;
  bool _isLoading = false;
  String? _searchQuery;
  List<Role> _filteredRoles = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInit) {
      _loadData();
      _isInit = true;
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<SecurityProvider>(context, listen: false).loadInitialData();
      _filterRoles();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطا در بارگذاری اطلاعات: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterRoles() {
    final securityProvider = Provider.of<SecurityProvider>(context, listen: false);
    final allRoles = securityProvider.roles;

    if (_searchQuery == null || _searchQuery!.isEmpty) {
      _filteredRoles = List.from(allRoles);
    } else {
      final query = _searchQuery!.toLowerCase();
      _filteredRoles = allRoles.where((role) {
        return role.name.toLowerCase().contains(query) ||
            (role.displayName?.toLowerCase().contains(query) ?? false) ||
            (role.description?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مدیریت نقش‌ها'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'بارگذاری مجدد',
          ),
          // دکمه افزودن نقش جدید - فقط برای کاربران دارای دسترسی
          PermissionGate(
            permissionName: 'roles.create',
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: () {
                // در اینجا می‌توان به صفحه افزودن نقش جدید هدایت کرد
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('این قابلیت هنوز پیاده‌سازی نشده است')),
                );
              },
              tooltip: 'افزودن نقش جدید',
            ),
            fallback: const SizedBox.shrink(),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildSearchBar(),
                Expanded(
                  child: Consumer<SecurityProvider>(
                    builder: (ctx, securityProvider, _) {
                      if (securityProvider.error != null) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'خطا در بارگذاری اطلاعات: ${securityProvider.error}',
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadData,
                                child: const Text('تلاش مجدد'),
                              ),
                            ],
                          ),
                        );
                      }

                      if (_filteredRoles.isEmpty) {
                        return const Center(
                          child: Text('هیچ نقشی یافت نشد.'),
                        );
                      }

                      return _buildRolesList();
                    },
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'جستجوی نقش...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          suffixIcon: _searchQuery != null && _searchQuery!.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    setState(() {
                      _searchQuery = null;
                    });
                    _filterRoles();
                  },
                )
              : null,
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
          _filterRoles();
        },
      ),
    );
  }

  Widget _buildRolesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredRoles.length,
      itemBuilder: (ctx, index) {
        final role = _filteredRoles[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text(
              role.displayNameOrName,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: role.description != null
                ? Text(
                    role.description!,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  )
                : Text('تعداد دسترسی‌ها: ${role.permissions.length}'),
            leading: const Icon(Icons.security),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // دکمه ویرایش - فقط برای کاربران دارای دسترسی
                PermissionGate(
                  permissionName: 'roles.edit',
                  child: IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      // در اینجا می‌توان به صفحه ویرایش نقش هدایت کرد
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('این قابلیت هنوز پیاده‌سازی نشده است')),
                      );
                    },
                    tooltip: 'ویرایش نقش',
                  ),
                  fallback: const SizedBox.shrink(),
                ),
                // دکمه حذف - فقط برای کاربران دارای دسترسی
                PermissionGate(
                  permissionName: 'roles.delete',
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () {
                      // در اینجا می‌توان عملیات حذف نقش را انجام داد
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('این قابلیت هنوز پیاده‌سازی نشده است')),
                      );
                    },
                    tooltip: 'حذف نقش',
                  ),
                  fallback: const SizedBox.shrink(),
                ),
              ],
            ),
            onTap: () {
              // هدایت به صفحه جزئیات نقش
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => RoleDetailsScreen(role: role),
                ),
              );
            },
          ),
        );
      },
    );
  }
}