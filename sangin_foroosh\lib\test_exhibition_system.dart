import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sangin_foroosh/core/providers/theme_provider.dart';
import 'package:sangin_foroosh/core/theme/app_theme.dart';
import 'package:sangin_foroosh/core/constants/app_colors.dart';
import 'package:sangin_foroosh/core/constants/app_dimensions.dart';
import 'package:sangin_foroosh/features/exhibition/widgets/active_exhibition_widget.dart';

import 'package:sangin_foroosh/features/exhibition/screens/exhibition_selection_screen.dart';
import 'package:sangin_foroosh/shared/widgets/exhibition_selector_widget.dart';
import 'package:sangin_foroosh/features/exhibition/providers/active_exhibition_provider.dart';

/// صفحه تست برای سیستم انتخاب نمایشگاه
class TestExhibitionSystemScreen extends StatelessWidget {
  const TestExhibitionSystemScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تست سیستم نمایشگاه'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        actions: const [
          SimpleActiveExhibitionWidget(),
          SizedBox(width: AppDimensions.paddingM),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // نمایش نمایشگاه فعال
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'نمایشگاه فعال',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'IranSansX',
                      ),
                    ),
                    const SizedBox(height: 16),
                    const ActiveExhibitionWidget(),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // انتخابگر نمایشگاه
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'انتخاب نمایشگاه',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'IranSansX',
                      ),
                    ),
                    const SizedBox(height: 16),
                    const ExhibitionSelectorWidget(
                      // navigateToRoute: (context, route, args) => Navigator.pushNamed(context, route, arguments: args),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // دکمه‌های تست
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'عملیات تست',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'IranSansX',
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // دکمه بارگذاری نمایشگاه‌ها
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          context.read<ActiveExhibitionProvider>().loadAvailableExhibitions();
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('بارگذاری مجدد نمایشگاه‌ها'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // دکمه پاک کردن نمایشگاه فعال
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          context.read<ActiveExhibitionProvider>().clearActiveExhibition();
                        },
                        icon: const Icon(Icons.clear),
                        label: const Text('پاک کردن نمایشگاه فعال'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // دکمه رفتن به صفحه انتخاب نمایشگاه
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ExhibitionSelectionScreen(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.event),
                        label: const Text('صفحه انتخاب نمایشگاه'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // اطلاعات وضعیت
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'وضعیت سیستم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'IranSansX',
                      ),
                    ),
                    const SizedBox(height: 16),
                    Consumer<ActiveExhibitionProvider>(
                      builder: (context, provider, child) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildStatusRow('نمایشگاه فعال:', 
                              provider.activeExhibition?.name ?? 'هیچ'),
                            _buildStatusRow('تعداد نمایشگاه‌ها:', 
                              provider.availableExhibitions.length.toString()),
                            _buildStatusRow('در حال بارگذاری:', 
                              provider.isLoading ? 'بله' : 'خیر'),
                            _buildStatusRow('خطا:', provider.error ?? 'ندارد'),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              fontFamily: 'IranSansX',
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontFamily: 'IranSansX',
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// اپلیکیشن تست برای سیستم نمایشگاه
class TestExhibitionApp extends StatelessWidget {
  const TestExhibitionApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => ActiveExhibitionProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) {
          return MaterialApp(
            title: 'تست سیستم نمایشگاه',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            home: const TestExhibitionSystemScreen(),
            builder: (context, child) {
              return Directionality(
                textDirection: TextDirection.rtl,
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}

void main() {
  runApp(const TestExhibitionApp());
}