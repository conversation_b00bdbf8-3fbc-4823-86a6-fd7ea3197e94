import '../../../core/services/api_service.dart';
import '../../../core/constants/app_config.dart';
import '../models/models.dart';

class AccountingRepository {
  final ApiService _apiService = ApiService();

  // Account Structure methods
  Future<Map<String, dynamic>> getAccountStructures({
    int page = 1,
    int perPage = 20,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await _apiService.dio.get(
        AppConfig.accountStructuresEndpoint,
        queryParameters: queryParams,
      );
      final List<dynamic> data = response.data['data'];
      final structures = data.map((json) => AccountStructure.fromJson(json)).toList();
      return {
        'structures': structures,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountStructure> getAccountStructure(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.accountStructuresEndpoint}/$id');
      return AccountStructure.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountStructure> createAccountStructure(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.accountStructuresEndpoint,
        data: data,
      );
      return AccountStructure.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountStructure> updateAccountStructure(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.accountStructuresEndpoint}/$id',
        data: data,
      );
      return AccountStructure.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteAccountStructure(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.accountStructuresEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Chart of Accounts methods
  Future<Map<String, dynamic>> getChartOfAccounts({
    int page = 1,
    int perPage = 20,
    String? search,
    int? accountStructureId,
    String? type,
    int? parentId,
    bool? isActive,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (accountStructureId != null) queryParams['account_structure_id'] = accountStructureId;
      if (type != null) queryParams['type'] = type;
      if (parentId != null) queryParams['parent_id'] = parentId;
      if (isActive != null) queryParams['is_active'] = isActive;

      final response = await _apiService.dio.get(
        AppConfig.chartOfAccountsEndpoint,
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final accounts = data.map((json) => ChartOfAccount.fromJson(json)).toList();
      
      return {
        'accounts': accounts,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<ChartOfAccount> getChartOfAccount(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.chartOfAccountsEndpoint}/$id');
      return ChartOfAccount.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<ChartOfAccount> createChartOfAccount(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.chartOfAccountsEndpoint,
        data: data,
      );
      return ChartOfAccount.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<ChartOfAccount> updateChartOfAccount(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.chartOfAccountsEndpoint}/$id',
        data: data,
      );
      return ChartOfAccount.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteChartOfAccount(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.chartOfAccountsEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Fiscal Year methods
  Future<List<FiscalYear>> getFiscalYears() async {
    try {
      final response = await _apiService.dio.get(AppConfig.fiscalYearsEndpoint);
      final List<dynamic> data = response.data['data'];
      return data.map((json) => FiscalYear.fromJson(json)).toList();
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FiscalYear> getFiscalYear(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.fiscalYearsEndpoint}/$id');
      return FiscalYear.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FiscalYear> createFiscalYear(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.fiscalYearsEndpoint,
        data: data,
      );
      return FiscalYear.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FiscalYear> updateFiscalYear(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.fiscalYearsEndpoint}/$id',
        data: data,
      );
      return FiscalYear.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteFiscalYear(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.fiscalYearsEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Accounting Document methods
  Future<Map<String, dynamic>> getAccountingDocuments({
    int page = 1,
    int perPage = 20,
    String? search,
    int? fiscalYearId,
    String? status,
    String? type,
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final response = await _apiService.getAccountingDocuments(
        page: page,
        perPage: perPage,
        search: search,
        fiscalYearId: fiscalYearId,
        status: status,
        type: type,
        fromDate: fromDate,
        toDate: toDate,
      );
      final List<dynamic> data = response['data'];
      final documents = data.map((json) => AccountingDocument.fromJson(json)).toList();
      return {
        'documents': documents,
        'pagination': response['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountingDocument> getAccountingDocument(int id) async {
    try {
      final response = await _apiService.getAccountingDocument(id);
      return AccountingDocument.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountingDocument> createAccountingDocument(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.createAccountingDocument(data);
      return AccountingDocument.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountingDocument> updateAccountingDocument(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.updateAccountingDocument(id, data);
      return AccountingDocument.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteAccountingDocument(int id) async {
    try {
      await _apiService.deleteAccountingDocument(id);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Bank methods
  Future<Map<String, dynamic>> getBanks({int page = 1, int perPage = 10, String? search}) async {
    try {
      final response = await _apiService.getBanks(page: page, perPage: perPage, search: search);
      final List<dynamic> data = response['data'];
      final banks = data.map((json) => Bank.fromJson(json)).toList();
      return {
        'banks': banks,
        'pagination': response['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Bank> createBank(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.createBank(data);
      return Bank.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Bank> updateBank(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.updateBank(id, data);
      return Bank.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteBank(int id) async {
    try {
      await _apiService.deleteBank(id);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Bank Account methods
  Future<Map<String, dynamic>> getBankAccounts({
    int page = 1,
    int perPage = 10,
    String? search,
    int? bankId,
    bool? isActive,
  }) async {
    try {
      final response = await _apiService.getBankAccounts(
        page: page,
        perPage: perPage,
        search: search,
        bankId: bankId,
        isActive: isActive,
      );
      final List<dynamic> data = response['data'];
      final accounts = data.map((json) => BankAccount.fromJson(json)).toList();
      return {
        'accounts': accounts,
        'pagination': response['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<BankAccount> createBankAccount(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.createBankAccount(data);
      return BankAccount.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<BankAccount> updateBankAccount(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.updateBankAccount(id, data);
      return BankAccount.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteBankAccount(int id) async {
    try {
      await _apiService.deleteBankAccount(id);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Cash Register methods
  Future<Map<String, dynamic>> getCashRegisters({
    int page = 1,
    int perPage = 10,
    String? search,
    bool? isActive,
  }) async {
    try {
      final response = await _apiService.getCashRegisters(
        page: page,
        perPage: perPage,
        search: search,
        isActive: isActive,
      );
      final List<dynamic> data = response['data'];
      final registers = data.map((json) => CashRegister.fromJson(json)).toList();
      return {
        'registers': registers,
        'pagination': response['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<CashRegister> createCashRegister(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.createCashRegister(data);
      return CashRegister.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<CashRegister> updateCashRegister(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.updateCashRegister(id, data);
      return CashRegister.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteCashRegister(int id) async {
    try {
      await _apiService.deleteCashRegister(id);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Accounting Document methods (fixed)
  Future<Map<String, dynamic>> getAccountingDocuments({
    int page = 1,
    int perPage = 20,
    String? search,
    int? fiscalYearId,
    String? status,
    String? type,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (fiscalYearId != null) queryParams['fiscal_year_id'] = fiscalYearId;
      if (status != null) queryParams['status'] = status;
      if (type != null) queryParams['type'] = type;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T').first;
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T').first;

      final response = await _apiService.dio.get(
        AppConfig.accountingDocumentsEndpoint,
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final documents = data.map((json) => AccountingDocument.fromJson(json)).toList();
      
      return {
        'documents': documents,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }





  // Bank Account methods
  Future<Map<String, dynamic>> getBankAccounts({
    int page = 1,
    int perPage = 20,
    String? search,
    int? bankId,
    String? accountType,
    bool? isActive,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (bankId != null) queryParams['bank_id'] = bankId;
      if (accountType != null) queryParams['account_type'] = accountType;
      if (isActive != null) queryParams['is_active'] = isActive;

      final response = await _apiService.dio.get(
        AppConfig.bankAccountsEndpoint,
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final accounts = data.map((json) => BankAccount.fromJson(json)).toList();
      
      return {
        'accounts': accounts,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<BankAccount> getBankAccount(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.bankAccountsEndpoint}/$id');
      return BankAccount.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<BankAccount> createBankAccount(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.bankAccountsEndpoint,
        data: data,
      );
      return BankAccount.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<BankAccount> updateBankAccount(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.bankAccountsEndpoint}/$id',
        data: data,
      );
      return BankAccount.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteBankAccount(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.bankAccountsEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Cash Register methods
  Future<List<CashRegister>> getCashRegisters() async {
    try {
      final response = await _apiService.dio.get(AppConfig.cashRegistersEndpoint);
      final List<dynamic> data = response.data['data'];
      return data.map((json) => CashRegister.fromJson(json)).toList();
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<CashRegister> getCashRegister(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.cashRegistersEndpoint}/$id');
      return CashRegister.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<CashRegister> createCashRegister(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.cashRegistersEndpoint,
        data: data,
      );
      return CashRegister.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<CashRegister> updateCashRegister(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.cashRegistersEndpoint}/$id',
        data: data,
      );
      return CashRegister.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteCashRegister(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.cashRegistersEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Checkbook methods
  Future<Map<String, dynamic>> getCheckbooks({
    int page = 1,
    int perPage = 20,
    String? search,
    int? bankAccountId,
    bool? isActive,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (bankAccountId != null) queryParams['bank_account_id'] = bankAccountId;
      if (isActive != null) queryParams['is_active'] = isActive;

      final response = await _apiService.dio.get(
        AppConfig.checkbooksEndpoint,
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final checkbooks = data.map((json) => Checkbook.fromJson(json)).toList();
      
      return {
        'checkbooks': checkbooks,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Checkbook> getCheckbook(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.checkbooksEndpoint}/$id');
      return Checkbook.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Checkbook> createCheckbook(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.checkbooksEndpoint,
        data: data,
      );
      return Checkbook.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Checkbook> updateCheckbook(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.checkbooksEndpoint}/$id',
        data: data,
      );
      return Checkbook.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteCheckbook(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.checkbooksEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Received Check methods
  Future<Map<String, dynamic>> getReceivedChecks({
    int page = 1,
    int perPage = 20,
    String? search,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (status != null) queryParams['status'] = status;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T').first;
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T').first;

      final response = await _apiService.dio.get(
        AppConfig.receivedChecksEndpoint,
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final checks = data.map((json) => ReceivedCheck.fromJson(json)).toList();
      
      return {
        'checks': checks,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<ReceivedCheck> getReceivedCheck(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.receivedChecksEndpoint}/$id');
      return ReceivedCheck.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<ReceivedCheck> createReceivedCheck(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.receivedChecksEndpoint,
        data: data,
      );
      return ReceivedCheck.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<ReceivedCheck> updateReceivedCheck(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.receivedChecksEndpoint}/$id',
        data: data,
      );
      return ReceivedCheck.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteReceivedCheck(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.receivedChecksEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Issued Check methods
  Future<Map<String, dynamic>> getIssuedChecks({
    int page = 1,
    int perPage = 20,
    String? search,
    int? checkbookId,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (checkbookId != null) queryParams['checkbook_id'] = checkbookId;
      if (status != null) queryParams['status'] = status;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T').first;
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T').first;

      final response = await _apiService.dio.get(
        AppConfig.issuedChecksEndpoint,
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final checks = data.map((json) => IssuedCheck.fromJson(json)).toList();
      
      return {
        'checks': checks,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<IssuedCheck> getIssuedCheck(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.issuedChecksEndpoint}/$id');
      return IssuedCheck.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<IssuedCheck> createIssuedCheck(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.issuedChecksEndpoint,
        data: data,
      );
      return IssuedCheck.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<IssuedCheck> updateIssuedCheck(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.issuedChecksEndpoint}/$id',
        data: data,
      );
      return IssuedCheck.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteIssuedCheck(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.issuedChecksEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Fund Transfer methods
  Future<Map<String, dynamic>> getFundTransfers({
    int page = 1,
    int perPage = 20,
    String? search,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (status != null) queryParams['status'] = status;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T').first;
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T').first;

      final response = await _apiService.dio.get(
        AppConfig.fundTransfersEndpoint,
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final transfers = data.map((json) => FundTransfer.fromJson(json)).toList();
      
      return {
        'transfers': transfers,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FundTransfer> getFundTransfer(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.fundTransfersEndpoint}/$id');
      return FundTransfer.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FundTransfer> createFundTransfer(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.fundTransfersEndpoint,
        data: data,
      );
      return FundTransfer.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FundTransfer> updateFundTransfer(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.fundTransfersEndpoint}/$id',
        data: data,
      );
      return FundTransfer.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteFundTransfer(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.fundTransfersEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Financial Transaction methods
  Future<Map<String, dynamic>> getFinancialTransactions({
    int page = 1,
    int perPage = 20,
    String? search,
    String? type,
    String? category,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (type != null) queryParams['type'] = type;
      if (category != null) queryParams['category'] = category;
      if (status != null) queryParams['status'] = status;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T').first;
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T').first;

      final response = await _apiService.dio.get(
        AppConfig.financialTransactionsEndpoint,
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final transactions = data.map((json) => FinancialTransaction.fromJson(json)).toList();
      
      return {
        'transactions': transactions,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FinancialTransaction> getFinancialTransaction(int id) async {
    try {
      final response = await _apiService.dio.get('${AppConfig.financialTransactionsEndpoint}/$id');
      return FinancialTransaction.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FinancialTransaction> createFinancialTransaction(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post(
        AppConfig.financialTransactionsEndpoint,
        data: data,
      );
      return FinancialTransaction.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FinancialTransaction> updateFinancialTransaction(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put(
        '${AppConfig.financialTransactionsEndpoint}/$id',
        data: data,
      );
      return FinancialTransaction.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteFinancialTransaction(int id) async {
    try {
      await _apiService.dio.delete('${AppConfig.financialTransactionsEndpoint}/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }
}