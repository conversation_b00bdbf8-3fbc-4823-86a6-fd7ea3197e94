import '../../../core/services/api_service.dart';
import '../models/models.dart';

class AccountingRepository {
  final ApiService _apiService = ApiService();

  // Account Structure methods
  Future<Map<String, dynamic>> getAccountStructures({
    int page = 1,
    int perPage = 20,
    String? search,
    int? accountStructureId,
    String? type,
    int? parentId,
    bool? isActive,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (accountStructureId != null) queryParams['account_structure_id'] = accountStructureId;
      if (type != null) queryParams['type'] = type;
      if (parentId != null) queryParams['parent_id'] = parentId;
      if (isActive != null) queryParams['is_active'] = isActive;

      final response = await _apiService.dio.get(
        '/api/account-structures',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['data'];
      final structures = data.map((json) => AccountStructure.fromJson(json)).toList();

      return {
        'structures': structures,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw Exception('Error loading account structures: $e');
    }
  }

  Future<AccountStructure> getAccountStructure(int id) async {
    try {
      final response = await _apiService.dio.get('/api/account-structures/$id');
      return AccountStructure.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountStructure> createAccountStructure(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post('/api/account-structures', data: data);
      return AccountStructure.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountStructure> updateAccountStructure(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put('/api/account-structures/$id', data: data);
      return AccountStructure.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteAccountStructure(int id) async {
    try {
      await _apiService.dio.delete('/api/account-structures/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Bank methods
  Future<List<Bank>> getBanks() async {
    try {
      final response = await _apiService.dio.get('/api/banks');
      final List<dynamic> data = response.data['data'];
      return data.map((json) => Bank.fromJson(json)).toList();
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Bank> getBank(int id) async {
    try {
      final response = await _apiService.dio.get('/api/banks/$id');
      return Bank.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Bank> createBank(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post('/api/banks', data: data);
      return Bank.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Bank> updateBank(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put('/api/banks/$id', data: data);
      return Bank.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteBank(int id) async {
    try {
      await _apiService.dio.delete('/api/banks/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Bank Account methods
  Future<Map<String, dynamic>> getBankAccounts({
    int page = 1,
    int perPage = 20,
    String? search,
    int? bankId,
    String? accountType,
    bool? isActive,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (bankId != null) queryParams['bank_id'] = bankId;
      if (accountType != null) queryParams['account_type'] = accountType;
      if (isActive != null) queryParams['is_active'] = isActive;

      final response = await _apiService.dio.get(
        '/api/bank-accounts',
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final accounts = data.map((json) => BankAccount.fromJson(json)).toList();
      
      return {
        'accounts': accounts,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<BankAccount> getBankAccount(int id) async {
    try {
      final response = await _apiService.dio.get('/api/bank-accounts/$id');
      return BankAccount.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<BankAccount> createBankAccount(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post('/api/bank-accounts', data: data);
      return BankAccount.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<BankAccount> updateBankAccount(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put('/api/bank-accounts/$id', data: data);
      return BankAccount.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteBankAccount(int id) async {
    try {
      await _apiService.dio.delete('/api/bank-accounts/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Cash Register methods
  Future<Map<String, dynamic>> getCashRegisters({
    int page = 1,
    int perPage = 20,
    String? search,
    int? chartOfAccountId,
    int? cashierId,
    bool? isActive,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (chartOfAccountId != null) queryParams['chart_of_account_id'] = chartOfAccountId;
      if (cashierId != null) queryParams['cashier_id'] = cashierId;
      if (isActive != null) queryParams['is_active'] = isActive;

      final response = await _apiService.dio.get(
        '/api/cash-registers',
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final cashRegisters = data.map((json) => CashRegister.fromJson(json)).toList();
      
      return {
        'cashRegisters': cashRegisters,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<CashRegister> getCashRegister(int id) async {
    try {
      final response = await _apiService.dio.get('/api/cash-registers/$id');
      return CashRegister.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<CashRegister> createCashRegister(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post('/api/cash-registers', data: data);
      return CashRegister.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<CashRegister> updateCashRegister(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put('/api/cash-registers/$id', data: data);
      return CashRegister.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteCashRegister(int id) async {
    try {
      await _apiService.dio.delete('/api/cash-registers/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Accounting Document methods
  Future<Map<String, dynamic>> getAccountingDocuments({
    int page = 1,
    int perPage = 20,
    String? search,
    int? fiscalYearId,
    String? status,
    String? type,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (fiscalYearId != null) queryParams['fiscal_year_id'] = fiscalYearId;
      if (status != null) queryParams['status'] = status;
      if (type != null) queryParams['type'] = type;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T').first;
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T').first;

      final response = await _apiService.dio.get(
        '/api/accounting-documents',
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      final documents = data.map((json) => AccountingDocument.fromJson(json)).toList();
      
      return {
        'documents': documents,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountingDocument> getAccountingDocument(int id) async {
    try {
      final response = await _apiService.dio.get('/api/accounting-documents/$id');
      return AccountingDocument.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountingDocument> createAccountingDocument(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post('/api/accounting-documents', data: data);
      return AccountingDocument.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountingDocument> updateAccountingDocument(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put('/api/accounting-documents/$id', data: data);
      return AccountingDocument.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteAccountingDocument(int id) async {
    try {
      await _apiService.dio.delete('/api/accounting-documents/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountingDocument> approveAccountingDocument(int id) async {
    try {
      final response = await _apiService.dio.post('/api/accounting-documents/$id/approve');
      return AccountingDocument.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<AccountingDocument> rejectAccountingDocument(int id) async {
    try {
      final response = await _apiService.dio.post('/api/accounting-documents/$id/reject');
      return AccountingDocument.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Chart of Account methods
  Future<Map<String, dynamic>> getChartOfAccounts({
    int page = 1,
    int perPage = 20,
    String? search,
    int? accountStructureId,
    String? type,
    int? parentId,
    bool? isActive,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (accountStructureId != null) queryParams['account_structure_id'] = accountStructureId;
      if (type != null) queryParams['type'] = type;
      if (parentId != null) queryParams['parent_id'] = parentId;
      if (isActive != null) queryParams['is_active'] = isActive;

      final response = await _apiService.dio.get(
        '/api/chart-of-accounts',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['data'];
      final accounts = data.map((json) => ChartOfAccount.fromJson(json)).toList();

      return {
        'accounts': accounts,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw Exception('Error loading chart of accounts: $e');
    }
  }

  Future<ChartOfAccount> getChartOfAccount(int id) async {
    try {
      final response = await _apiService.dio.get('/api/chart-of-accounts/$id');
      return ChartOfAccount.fromJson(response.data['data']);
    } catch (e) {
      throw Exception('Error loading chart of account: $e');
    }
  }

  Future<ChartOfAccount> createChartOfAccount(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post('/api/chart-of-accounts', data: data);
      return ChartOfAccount.fromJson(response.data['data']);
    } catch (e) {
      throw Exception('Error creating chart of account: $e');
    }
  }

  Future<ChartOfAccount> updateChartOfAccount(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put('/api/chart-of-accounts/$id', data: data);
      return ChartOfAccount.fromJson(response.data['data']);
    } catch (e) {
      throw Exception('Error updating chart of account: $e');
    }
  }

  Future<void> deleteChartOfAccount(int id) async {
    try {
      await _apiService.dio.delete('/api/chart-of-accounts/$id');
    } catch (e) {
      throw Exception('Error deleting chart of account: $e');
    }
  }

  // Checkbook methods
  Future<Map<String, dynamic>> getCheckbooks({
    int page = 1,
    int perPage = 20,
    String? search,
    int? bankAccountId,
    bool? isActive,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (bankAccountId != null) queryParams['bank_account_id'] = bankAccountId;
      if (isActive != null) queryParams['is_active'] = isActive;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T').first;
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T').first;

      final response = await _apiService.dio.get(
        '/api/checkbooks',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['data'];
      final checkbooks = data.map((json) => Checkbook.fromJson(json)).toList();

      return {
        'checkbooks': checkbooks,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw Exception('Error loading checkbooks: $e');
    }
  }

  Future<Checkbook> getCheckbook(int id) async {
    try {
      final response = await _apiService.dio.get('/api/checkbooks/$id');
      return Checkbook.fromJson(response.data['data']);
    } catch (e) {
      throw Exception('Error loading checkbook: $e');
    }
  }

  Future<Checkbook> createCheckbook(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post('/api/checkbooks', data: data);
      return Checkbook.fromJson(response.data['data']);
    } catch (e) {
      throw Exception('Error creating checkbook: $e');
    }
  }

  Future<Checkbook> updateCheckbook(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put('/api/checkbooks/$id', data: data);
      return Checkbook.fromJson(response.data['data']);
    } catch (e) {
      throw Exception('Error updating checkbook: $e');
    }
  }

  Future<void> deleteCheckbook(int id) async {
    try {
      await _apiService.dio.delete('/api/checkbooks/$id');
    } catch (e) {
      throw Exception('Error deleting checkbook: $e');
    }
  }

  // Financial Transaction methods
  Future<Map<String, dynamic>> getFinancialTransactions({
    int page = 1,
    int perPage = 20,
    String? search,
    String? type,
    String? category,
    String? status,
    String? paymentMethod,
    int? contactId,
    DateTime? fromDate,
    DateTime? toDate,
    double? minAmount,
    double? maxAmount,
    List<String>? tags,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (type != null) queryParams['type'] = type;
      if (category != null) queryParams['category'] = category;
      if (status != null) queryParams['status'] = status;
      if (paymentMethod != null) queryParams['payment_method'] = paymentMethod;
      if (contactId != null) queryParams['contact_id'] = contactId;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T').first;
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T').first;
      if (minAmount != null) queryParams['min_amount'] = minAmount;
      if (maxAmount != null) queryParams['max_amount'] = maxAmount;
      if (tags != null && tags.isNotEmpty) queryParams['tags'] = tags.join(',');

      final response = await _apiService.dio.get(
        '/api/financial-transactions',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['data'];
      final transactions = data.map((json) => FinancialTransaction.fromJson(json)).toList();

      return {
        'transactions': transactions,
        'pagination': response.data['meta'] ?? {},
      };
    } catch (e) {
      throw Exception('Error loading financial transactions: $e');
    }
  }

  Future<FinancialTransaction> getFinancialTransaction(int id) async {
    try {
      final response = await _apiService.dio.get('/api/financial-transactions/$id');
      return FinancialTransaction.fromJson(response.data['data']);
    } catch (e) {
      throw Exception('Error loading financial transaction: $e');
    }
  }

  Future<FinancialTransaction> createFinancialTransaction(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post('/api/financial-transactions', data: data);
      return FinancialTransaction.fromJson(response.data['data']);
    } catch (e) {
      throw Exception('Error creating financial transaction: $e');
    }
  }

  Future<FinancialTransaction> updateFinancialTransaction(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put('/api/financial-transactions/$id', data: data);
      return FinancialTransaction.fromJson(response.data['data']);
    } catch (e) {
      throw Exception('Error updating financial transaction: $e');
    }
  }

  Future<void> deleteFinancialTransaction(int id) async {
    try {
      await _apiService.dio.delete('/api/financial-transactions/$id');
    } catch (e) {
      throw Exception('Error deleting financial transaction: $e');
    }
  }

  // Fiscal Year methods
  Future<List<FiscalYear>> getFiscalYears() async {
    try {
      final response = await _apiService.dio.get('/api/fiscal-years');
      final List<dynamic> data = response.data['data'];
      return data.map((json) => FiscalYear.fromJson(json)).toList();
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FiscalYear> getFiscalYear(int id) async {
    try {
      final response = await _apiService.dio.get('/api/fiscal-years/$id');
      return FiscalYear.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FiscalYear> createFiscalYear(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.post('/api/fiscal-years', data: data);
      return FiscalYear.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<FiscalYear> updateFiscalYear(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.dio.put('/api/fiscal-years/$id', data: data);
      return FiscalYear.fromJson(response.data['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteFiscalYear(int id) async {
    try {
      await _apiService.dio.delete('/api/fiscal-years/$id');
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }
}
