import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../models/machinery.dart';
import '../providers/machinery_provider.dart';
import '../widgets/machinery_card.dart';
import '../widgets/machinery_form.dart';
import '../widgets/machinery_filter.dart';

class MachineryListScreen extends StatefulWidget {
  const MachineryListScreen({super.key});

  @override
  State<MachineryListScreen> createState() => _MachineryListScreenState();
}

class _MachineryListScreenState extends State<MachineryListScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  MachineryFilter _currentFilter = MachineryFilter();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadInitialData() {
    final provider = Provider.of<MachineryProvider>(context, listen: false);
    provider.loadMachineries();
    provider.loadCategories();
    provider.loadStatistics();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      final provider = Provider.of<MachineryProvider>(context, listen: false);
      if (provider.hasMore && !provider.isLoading) {
        provider.loadMoreMachineries();
      }
    }
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    final provider = Provider.of<MachineryProvider>(context, listen: false);
    if (query.isNotEmpty) {
      provider.searchMachineries(
        query: query,
        category: _currentFilter.category,
        status: _currentFilter.status,
        minPrice: _currentFilter.minPrice,
        maxPrice: _currentFilter.maxPrice,
        minYear: _currentFilter.minYear,
        maxYear: _currentFilter.maxYear,
      );
    } else {
      provider.loadMachineries(refresh: true);
    }
  }

  void _applyFilter(MachineryFilter filter) {
    setState(() {
      _currentFilter = filter;
    });
    final provider = Provider.of<MachineryProvider>(context, listen: false);
    if (_searchQuery.isNotEmpty) {
      provider.searchMachineries(
        query: _searchQuery,
        category: filter.category,
        status: filter.status,
        minPrice: filter.minPrice,
        maxPrice: filter.maxPrice,
        minYear: filter.minYear,
        maxYear: filter.maxYear,
      );
    } else {
      provider.loadMachineries(refresh: true);
    }
  }

  void _clearFilter() {
    setState(() {
      _currentFilter = MachineryFilter();
    });
    final provider = Provider.of<MachineryProvider>(context, listen: false);
    if (_searchQuery.isNotEmpty) {
      provider.searchMachineries(query: _searchQuery);
    } else {
      provider.loadMachineries(refresh: true);
    }
  }

  void _showFilterDialog() {
    final provider = Provider.of<MachineryProvider>(context, listen: false);
    // بارگذاری سازندگان و مکان‌ها اگر هنوز بارگذاری نشده‌اند
    if (provider.manufacturers.isEmpty) {
      provider.loadManufacturers();
    }
    if (provider.locations.isEmpty) {
      provider.loadLocations();
    }
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MachineryFilterWidget(
        initialFilter: _currentFilter,
        categories: provider.categories,
        manufacturers: provider.manufacturers,
        locations: provider.locations,
        onApplyFilter: _applyFilter,
        onClearFilter: _clearFilter,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'ماشین‌آلات',
          style: TextStyle(
            fontFamily: 'IranSansX',
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Implement notifications
            },
          ),
        ],
      ),
      body: Consumer<MachineryProvider>(
        builder: (context, provider, child) {
          return LoadingOverlay(
            isLoading: provider.isLoading && provider.machineries.isEmpty,
            child: Column(
              children: [
                _buildSearchAndFilter(provider),
                _buildStatistics(provider),
                Expanded(
                  child: _buildMachineryList(provider),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showMachineryDialog(),
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter(MachineryProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'جستجو در ماشین‌آلات...',
                    hintStyle: const TextStyle(
                      fontFamily: 'IranSansX',
                      color: AppColors.textSecondary,
                    ),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: AppColors.textSecondary,
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(
                              Icons.clear,
                              color: AppColors.textSecondary,
                            ),
                            onPressed: () {
                              _searchController.clear();
                              _performSearch('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppColors.border),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppColors.border),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppColors.primary),
                    ),
                    filled: true,
                    fillColor: AppColors.background,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  style: const TextStyle(
                    fontFamily: 'IranSansX',
                  ),
                  onChanged: _performSearch,
                ),
              ),
              const SizedBox(width: 12),
              Container(
                decoration: BoxDecoration(
                  color: _currentFilter.hasActiveFilters
                      ? AppColors.primary
                      : AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: Icon(
                    Icons.filter_list,
                    color: _currentFilter.hasActiveFilters
                        ? Colors.white
                        : AppColors.textSecondary,
                  ),
                  onPressed: _showFilterDialog,
                ),
              ),
            ],
          ),
          if (provider.categories.isNotEmpty) ...[
            const SizedBox(height: 12),
            SizedBox(
              height: 32,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: provider.categories.length + 1,
                itemBuilder: (context, index) {
                  if (index == 0) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: _buildFilterChip(
                        'همه',
                        _currentFilter.category == null,
                        () {
                          final newFilter = _currentFilter.copy();
                          newFilter.category = null;
                          _applyFilter(newFilter);
                        },
                      ),
                    );
                  }
                  final category = provider.categories[index - 1];
                  return Padding(
                    padding: const EdgeInsets.only(left: 8),
                    child: _buildFilterChip(
                      category,
                      _currentFilter.category == category,
                      () {
                        final newFilter = _currentFilter.copy();
                        newFilter.category = category;
                        _applyFilter(newFilter);
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.background,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontFamily: 'IranSansX',
            fontSize: 12,
            color: isSelected ? Colors.white : AppColors.textSecondary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildStatistics(MachineryProvider provider) {
    if (provider.statistics == null) return const SizedBox.shrink();

    final stats = provider.statistics!;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildStatItem('کل', stats.total.toString(), AppColors.primary),
          _buildStatItem('در دسترس', stats.available.toString(), AppColors.success),
          _buildStatItem('اجاره شده', stats.rented.toString(), AppColors.warning),
          _buildStatItem('تعمیر', stats.maintenance.toString(), AppColors.error),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Expanded(
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'IranSansX',
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
              fontFamily: 'IranSansX',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMachineryList(MachineryProvider provider) {
    if (provider.error != null) {
      return _buildErrorWidget(provider.error!, () {
        provider.clearError();
        _loadInitialData();
      });
    }

    if (provider.machineries.isEmpty && !provider.isLoading) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () async {
        if (_searchQuery.isNotEmpty) {
          await provider.searchMachineries(
            query: _searchQuery,
            category: _currentFilter.category,
            status: _currentFilter.status,
            minPrice: _currentFilter.minPrice,
            maxPrice: _currentFilter.maxPrice,
            minYear: _currentFilter.minYear,
            maxYear: _currentFilter.maxYear,
          );
        } else {
          await provider.loadMachineries(refresh: true);
        }
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: provider.machineries.length + (provider.hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= provider.machineries.length) {
            return const Padding(
              padding: EdgeInsets.all(16),
              child: Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              ),
            );
          }

          final machinery = provider.machineries[index];
          return MachineryCard(
            machinery: machinery,
            onTap: () => _showMachineryDetail(machinery),
            onEdit: () => _showMachineryDialog(machinery: machinery),
            onDelete: () => _showDeleteConfirmationDialog(machinery.id),
            onFavorite: () {
              // TODO: Implement favorite functionality
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'علاقه‌مندی ${machinery.name}',
                    style: const TextStyle(fontFamily: 'IranSansX'),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 64,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 16),
            Text(
              'هیچ ماشین‌آلاتی یافت نشد',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
                fontFamily: 'IranSansX',
              ),
            ),
            SizedBox(height: 8),
            Text(
              'برای افزودن ماشین‌آلات جدید روی دکمه + کلیک کنید',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
                fontFamily: 'IranSansX',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showMachineryDetail(Machinery machinery) {
    // TODO: Implement detail screen navigation
     ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'جزئیات ${machinery.name}',
          style: const TextStyle(fontFamily: 'IranSansX'),
        ),
      ),
    );
  }

  Future<void> _showMachineryDialog({Machinery? machinery}) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            machinery == null ? 'ایجاد ماشین جدید' : 'ویرایش ماشین',
            style: const TextStyle(fontFamily: 'IranSansX'),
          ),
          content: MachineryForm(machinery: machinery),
        );
      },
    );

    if (result == true) {
      _showSuccessSnackBar(
        machinery == null
            ? 'ماشین با موفقیت ایجاد شد.'
            : 'ماشین با موفقیت به‌روزرسانی شد.',
      );
      // No need to call refresh here as provider should have updated the list
    }
  }

  Future<void> _showDeleteConfirmationDialog(int machineryId) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تایید حذف', style: TextStyle(fontFamily: 'IranSansX')),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text('آیا از حذف این ماشین اطمینان دارید؟', style: TextStyle(fontFamily: 'IranSansX')),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('انصراف', style: TextStyle(fontFamily: 'IranSansX')),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('حذف', style: TextStyle(fontFamily: 'IranSansX', color: AppColors.error)),
              onPressed: () async {
                Navigator.of(context).pop();
                await _deleteMachinery(machineryId);
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteMachinery(int machineryId) async {
    final provider = Provider.of<MachineryProvider>(context, listen: false);
    try {
      await provider.deleteMachinery(machineryId);
      _showSuccessSnackBar('ماشین با موفقیت حذف شد.');
    } catch (e) {
      _showErrorSnackBar('خطا در حذف ماشین: ${e.toString()}');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'IranSansX')),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'IranSansX')),
        backgroundColor: AppColors.error,
      ),
    );
  }

  Widget _buildErrorWidget(String error, VoidCallback onRetry) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'خطا در بارگذاری اطلاعات',
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textPrimary,
                fontFamily: 'IranSansX',
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
                fontFamily: 'IranSansX',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'تلاش مجدد',
                style: TextStyle(
                  fontFamily: 'IranSansX',
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}