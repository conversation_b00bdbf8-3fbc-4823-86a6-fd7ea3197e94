import 'package:flutter/material.dart';
import '../models/models.dart';
import '../repositories/accounting_repository.dart';

class AccountStructureProvider extends ChangeNotifier {
  final AccountingRepository _repository;
  
  AccountStructureProvider() : _repository = AccountingRepository();

  List<AccountStructure> _accountStructures = [];
  AccountStructure? _selectedAccountStructure;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _pagination = {};

  // Getters
  List<AccountStructure> get accountStructures => _accountStructures;
  AccountStructure? get selectedAccountStructure => _selectedAccountStructure;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic> get pagination => _pagination;
  bool get hasError => _error != null;
  bool get isEmpty => _accountStructures.isEmpty && !_isLoading;

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error state
  void _setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  // Load account structures
  Future<void> loadAccountStructures({
    int page = 1,
    int perPage = 20,
    String? search,
    bool refresh = false,
  }) async {
    try {
      // Set loading state for all requests to prevent concurrent calls
      // and allow UI to show appropriate loading indicators.
      _setLoading(true);

      if (refresh || page == 1) {
        _error = null;
      }

      final result = await _repository.getAccountStructures(
        page: page,
        perPage: perPage,
        search: search,
      );

      final List<AccountStructure> structures = List<AccountStructure>.from(result['structures']);
      if (page == 1 || refresh) {
        _accountStructures = structures;
      } else {
        _accountStructures.addAll(structures);
      }
      
      _pagination = result['pagination'];
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری ساختار حساب‌ها: ${e.toString()}');
    }
  }

  // Load account structure by ID
  Future<void> loadAccountStructure(int id) async {
    try {
      _setLoading(true);
      _error = null;

      _selectedAccountStructure = await _repository.getAccountStructure(id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری ساختار حساب: ${e.toString()}');
    }
  }

  // Create account structure
  Future<bool> createAccountStructure(Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final newStructure = await _repository.createAccountStructure(data);
      _accountStructures.insert(0, newStructure);
      _selectedAccountStructure = newStructure;
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ایجاد ساختار حساب: ${e.toString()}');
      return false;
    }
  }

  // Update account structure
  Future<bool> updateAccountStructure(int id, Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final updatedStructure = await _repository.updateAccountStructure(id, data);
      
      final index = _accountStructures.indexWhere((s) => s.id == id);
      if (index != -1) {
        _accountStructures[index] = updatedStructure;
      }
      
      if (_selectedAccountStructure?.id == id) {
        _selectedAccountStructure = updatedStructure;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ویرایش ساختار حساب: ${e.toString()}');
      return false;
    }
  }

  // Delete account structure
  Future<bool> deleteAccountStructure(int id) async {
    try {
      _setLoading(true);
      _error = null;

      await _repository.deleteAccountStructure(id);
      
      _accountStructures.removeWhere((s) => s.id == id);
      
      if (_selectedAccountStructure?.id == id) {
        _selectedAccountStructure = null;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در حذف ساختار حساب: ${e.toString()}');
      return false;
    }
  }

  // Select account structure
  void selectAccountStructure(AccountStructure? structure) {
    _selectedAccountStructure = structure;
    notifyListeners();
  }

  // Clear selected account structure
  void clearSelection() {
    _selectedAccountStructure = null;
    notifyListeners();
  }

  // Search account structures
  Future<void> searchAccountStructures(String query) async {
    await loadAccountStructures(search: query, refresh: true);
  }

  // Refresh account structures
  Future<void> refresh() async {
    await loadAccountStructures(refresh: true);
  }

  // Load more account structures (pagination)
  Future<void> loadMore() async {
    if (_isLoading) return;
    
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    
    if (currentPage < lastPage) {
      await loadAccountStructures(page: currentPage + 1);
    }
  }

  // Check if can load more
  bool get canLoadMore {
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    return currentPage < lastPage && !_isLoading;
  }

  // Get account structure by ID from loaded list
  AccountStructure? getAccountStructureById(int id) {
    try {
      return _accountStructures.firstWhere((s) => s.id == id);
    } catch (e) {
      return null;
    }
  }


}