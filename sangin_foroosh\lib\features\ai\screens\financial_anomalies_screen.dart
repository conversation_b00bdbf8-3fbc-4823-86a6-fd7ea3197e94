import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:provider/provider.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:intl/intl.dart';
import '../providers/ai_provider.dart';
import '../../accounting/models/financial_anomaly.dart';
import '../widgets/anomaly_filter_dialog.dart';
import '../widgets/anomaly_details_dialog.dart';

/// صفحه نمایش ناهنجاری‌های مالی
class FinancialAnomaliesScreen extends StatefulWidget {
  const FinancialAnomaliesScreen({super.key});
  
  @override
  State<FinancialAnomaliesScreen> createState() => _FinancialAnomaliesScreenState();
}

class _FinancialAnomaliesScreenState extends State<FinancialAnomaliesScreen> {
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  bool _isLoadingMore = false;
  
  // فیلترها
  String? _selectedSeverity;
  String? _selectedStatus;
  String? _selectedCategory;
  DateTime? _dateFrom;
  DateTime? _dateTo;
  double? _minAmount;
  double? _maxAmount;
  
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAnomalies();
    });
  }
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
  
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreAnomalies();
    }
  }
  
  Future<void> _loadAnomalies() async {
    final aiProvider = Provider.of<AIProvider>(context, listen: false);
    await aiProvider.loadFinancialAnomalies(
      page: 1,
      severity: _selectedSeverity,
      status: _selectedStatus,
      category: _selectedCategory,
      dateFrom: _dateFrom,
      dateTo: _dateTo,
      minAmount: _minAmount,
      maxAmount: _maxAmount,
    );
    _currentPage = 1;
  }
  
  Future<void> _loadMoreAnomalies() async {
    if (_isLoadingMore) return;
    
    setState(() {
      _isLoadingMore = true;
    });
    
    final aiProvider = Provider.of<AIProvider>(context, listen: false);
    await aiProvider.loadFinancialAnomalies(
      page: _currentPage + 1,
      severity: _selectedSeverity,
      status: _selectedStatus,
      category: _selectedCategory,
      dateFrom: _dateFrom,
      dateTo: _dateTo,
      minAmount: _minAmount,
      maxAmount: _maxAmount,
    );
    
    _currentPage++;
    setState(() {
      _isLoadingMore = false;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ناهنجاری‌های مالی'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(TablerIcons.filter),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(TablerIcons.refresh),
            onPressed: _loadAnomalies,
          ),
        ],
      ),
      body: Consumer<AIProvider>(
        builder: (context, aiProvider, child) {
          if (aiProvider.isLoadingAnomalies && aiProvider.financialAnomalies.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          if (aiProvider.errorMessage != null && aiProvider.financialAnomalies.isEmpty) {
            return _buildErrorWidget(aiProvider.errorMessage!);
          }
          
          return Column(
            children: [
              // آمار کلی
              _buildStatisticsCard(aiProvider),
              
              // فیلترهای فعال
              if (_hasActiveFilters()) _buildActiveFilters(),
              
              // جدول داده‌ها
              Expanded(
                child: _buildDataTable(aiProvider),
              ),
            ],
          );
        },
      ),
    );
  }
  
  Widget _buildStatisticsCard(AIProvider aiProvider) {
    final stats = aiProvider.getAnomaliesStatistics();
    
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'آمار کلی ناهنجاری‌ها',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'کل موارد',
                    stats['total'].toString(),
                    Colors.blue,
                    TablerIcons.list,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'در انتظار بررسی',
                    stats['pending'].toString(),
                    Colors.orange,
                    TablerIcons.clock,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'حل شده',
                    stats['resolved'].toString(),
                    Colors.green,
                    TablerIcons.check,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'بحرانی',
                    stats['critical'].toString(),
                    Colors.red,
                    TablerIcons.alert_triangle,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStatItem(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildActiveFilters() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Wrap(
        spacing: 8,
        children: [
          if (_selectedSeverity != null)
            Chip(
              label: Text('شدت: $_selectedSeverity'),
              onDeleted: () {
                setState(() {
                  _selectedSeverity = null;
                });
                _loadAnomalies();
              },
            ),
          if (_selectedStatus != null)
            Chip(
              label: Text('وضعیت: $_selectedStatus'),
              onDeleted: () {
                setState(() {
                  _selectedStatus = null;
                });
                _loadAnomalies();
              },
            ),
          if (_selectedCategory != null)
            Chip(
              label: Text('دسته: $_selectedCategory'),
              onDeleted: () {
                setState(() {
                  _selectedCategory = null;
                });
                _loadAnomalies();
              },
            ),
          if (_dateFrom != null || _dateTo != null)
            Chip(
              label: const Text('فیلتر تاریخ'),
              onDeleted: () {
                setState(() {
                  _dateFrom = null;
                  _dateTo = null;
                });
                _loadAnomalies();
              },
            ),
        ],
      ),
    );
  }
  
  Widget _buildDataTable(AIProvider aiProvider) {
    final anomalies = aiProvider.financialAnomalies;
    
    if (anomalies.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.database_off,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'هیچ ناهنجاری مالی یافت نشد',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
    
    return Card(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          Expanded(
            child: DataTable2(
              controller: _scrollController,
              columnSpacing: 12,
              horizontalMargin: 12,
              minWidth: 800,
              columns: const [
                DataColumn2(
                  label: Text('تاریخ'),
                  size: ColumnSize.S,
                ),
                DataColumn2(
                  label: Text('مبلغ'),
                  size: ColumnSize.M,
                ),
                DataColumn2(
                  label: Text('توضیحات'),
                  size: ColumnSize.L,
                ),
                DataColumn2(
                  label: Text('دلیل پرچم‌گذاری'),
                  size: ColumnSize.L,
                ),
                DataColumn2(
                  label: Text('شدت'),
                  size: ColumnSize.S,
                ),
                DataColumn2(
                  label: Text('وضعیت'),
                  size: ColumnSize.S,
                ),
                DataColumn2(
                  label: Text('عملیات'),
                  size: ColumnSize.S,
                ),
              ],
              rows: anomalies.map((anomaly) {
                return DataRow2(
                  color: MaterialStateProperty.resolveWith<Color?>(
                    (Set<MaterialState> states) {
                      return _getRowColor(anomaly.severity);
                    },
                  ),
                  cells: [
                    DataCell(
                      Text(_formatDate(anomaly.detectedAt)),
                    ),
                    DataCell(
                      Text(
                        _formatAmount(anomaly.amount),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _getAmountColor(anomaly.amount),
                        ),
                      ),
                    ),
                    DataCell(
                      Text(
                        anomaly.description,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    DataCell(
                      Text(
                        anomaly.flagReason,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    DataCell(
                      _buildSeverityChip(anomaly.severity),
                    ),
                    DataCell(
                      _buildStatusChip(anomaly.status),
                    ),
                    DataCell(
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(TablerIcons.eye, size: 16),
                            onPressed: () => _showAnomalyDetails(anomaly),
                            tooltip: 'مشاهده جزئیات',
                          ),
                          IconButton(
                            icon: const Icon(TablerIcons.edit, size: 16),
                            onPressed: () => _updateAnomalyStatus(anomaly),
                            tooltip: 'ویرایش وضعیت',
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
          if (_isLoadingMore)
            const Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
  
  Widget _buildSeverityChip(String severity) {
    Color color;
    switch (severity.toLowerCase()) {
      case 'critical':
        color = Colors.red;
        break;
      case 'high':
        color = Colors.orange;
        break;
      case 'medium':
        color = Colors.yellow;
        break;
      case 'low':
        color = Colors.green;
        break;
      default:
        color = Colors.grey;
    }
    
    return Chip(
      label: Text(
        _getSeverityLabel(severity),
        style: const TextStyle(
          fontSize: 12,
          color: Colors.white,
        ),
      ),
      backgroundColor: color,
    );
  }
  
  Widget _buildStatusChip(String status) {
    Color color;
    switch (status.toLowerCase()) {
      case 'pending':
        color = Colors.orange;
        break;
      case 'reviewed':
        color = Colors.blue;
        break;
      case 'resolved':
        color = Colors.green;
        break;
      case 'false_positive':
        color = Colors.grey;
        break;
      default:
        color = Colors.grey;
    }
    
    return Chip(
      label: Text(
        _getStatusLabel(status),
        style: const TextStyle(
          fontSize: 12,
          color: Colors.white,
        ),
      ),
      backgroundColor: color,
    );
  }
  
  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            TablerIcons.alert_circle,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'خطا در بارگذاری داده‌ها',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadAnomalies,
            child: const Text('تلاش مجدد'),
          ),
        ],
      ),
    );
  }
  
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AnomalyFilterDialog(
        selectedSeverity: _selectedSeverity,
        selectedStatus: _selectedStatus,
        selectedCategory: _selectedCategory,
        dateFrom: _dateFrom,
        dateTo: _dateTo,
        minAmount: _minAmount,
        maxAmount: _maxAmount,
        onApply: (filters) {
          setState(() {
            _selectedSeverity = filters['severity'];
            _selectedStatus = filters['status'];
            _selectedCategory = filters['category'];
            _dateFrom = filters['dateFrom'];
            _dateTo = filters['dateTo'];
            _minAmount = filters['minAmount'];
            _maxAmount = filters['maxAmount'];
          });
          _loadAnomalies();
        },
      ),
    );
  }
  
  void _showAnomalyDetails(FinancialAnomaly anomaly) {
    showDialog(
      context: context,
      builder: (context) => AnomalyDetailsDialog(anomaly: anomaly),
    );
  }
  
  void _updateAnomalyStatus(FinancialAnomaly anomaly) {
    showDialog(
      context: context,
      builder: (context) => _UpdateStatusDialog(
        anomaly: anomaly,
        onUpdate: (status, notes) async {
          final aiProvider = Provider.of<AIProvider>(context, listen: false);
          final success = await aiProvider.updateAnomalyStatus(
            anomaly.id,
            status,
            notes: notes,
          );
          
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('وضعیت با موفقیت به‌روزرسانی شد'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطا: ${aiProvider.errorMessage}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }
  
  bool _hasActiveFilters() {
    return _selectedSeverity != null ||
        _selectedStatus != null ||
        _selectedCategory != null ||
        _dateFrom != null ||
        _dateTo != null ||
        _minAmount != null ||
        _maxAmount != null;
  }
  
  Color? _getRowColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
        return Colors.red.withOpacity(0.1);
      case 'high':
        return Colors.orange.withOpacity(0.1);
      case 'medium':
        return Colors.yellow.withOpacity(0.1);
      default:
        return null;
    }
  }
  
  Color _getAmountColor(double amount) {
    if (amount > **********) return Colors.red; // بیش از یک میلیارد
    if (amount > 100000000) return Colors.orange; // بیش از صد میلیون
    return Colors.black;
  }
  
  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }
  
  String _formatAmount(double amount) {
    final formatter = NumberFormat('#,###');
    return '${formatter.format(amount)} تومان';
  }
  
  String _getSeverityLabel(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'بحرانی';
      case 'high':
        return 'بالا';
      case 'medium':
        return 'متوسط';
      case 'low':
        return 'پایین';
      default:
        return severity;
    }
  }
  
  String _getStatusLabel(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'در انتظار';
      case 'reviewed':
        return 'بررسی شده';
      case 'resolved':
        return 'حل شده';
      case 'false_positive':
        return 'مثبت کاذب';
      default:
        return status;
    }
  }
}

/// دیالوگ به‌روزرسانی وضعیت ناهنجاری
class _UpdateStatusDialog extends StatefulWidget {
  final FinancialAnomaly anomaly;
  final Function(String status, String? notes) onUpdate;
  
  const _UpdateStatusDialog({
    required this.anomaly,
    required this.onUpdate,
  });
  
  @override
  State<_UpdateStatusDialog> createState() => _UpdateStatusDialogState();
}

class _UpdateStatusDialogState extends State<_UpdateStatusDialog> {
  String _selectedStatus = 'pending';
  final TextEditingController _notesController = TextEditingController();
  
  final List<Map<String, String>> _statusOptions = [
    {'value': 'pending', 'label': 'در انتظار بررسی'},
    {'value': 'reviewed', 'label': 'بررسی شده'},
    {'value': 'resolved', 'label': 'حل شده'},
    {'value': 'false_positive', 'label': 'مثبت کاذب'},
  ];
  
  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.anomaly.status;
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('به‌روزرسانی وضعیت ناهنجاری'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ناهنجاری: ${widget.anomaly.description}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          const Text('وضعیت جدید:'),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedStatus,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            items: _statusOptions.map((option) {
              return DropdownMenuItem<String>(
                value: option['value'],
                child: Text(option['label']!),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedStatus = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          const Text('یادداشت (اختیاری):'),
          const SizedBox(height: 8),
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'توضیحات اضافی...',
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('انصراف'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onUpdate(
              _selectedStatus,
              _notesController.text.isNotEmpty ? _notesController.text : null,
            );
            Navigator.of(context).pop();
          },
          child: const Text('به‌روزرسانی'),
        ),
      ],
    );
  }
}