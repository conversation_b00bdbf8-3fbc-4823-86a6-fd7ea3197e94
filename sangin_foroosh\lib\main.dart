import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';

import 'core/constants/app_config.dart';
import 'core/theme/app_theme.dart';
import 'core/providers/theme_provider.dart';
import 'core/services/api_service.dart';
import 'features/auth/providers/auth_provider.dart';
import 'features/machinery/providers/machinery_provider.dart';
import 'features/ai/providers/ai_provider.dart';
import 'features/ai/services/ai_service.dart';
import 'features/crm/providers/crm_provider.dart';

import 'core/providers/active_exhibition_provider.dart';
import 'features/auth/screens/login_screen.dart';
import 'test_exhibition_system.dart';
import 'features/main/screens/main_shell.dart';
import 'features/exhibition/screens/exhibition_selection_screen.dart';
import 'features/ai/screens/financial_anomalies_screen.dart';
import 'features/ai/screens/ai_dashboard_screen.dart';
import 'features/ai/screens/smart_query_screen.dart';
import 'features/ai/screens/ai_agents_screen.dart';
import 'features/ai/screens/agent_chat_screen.dart';
import 'features/crm/screens/contact_detail_screen.dart';
import 'test_api_screen.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Set preferred orientations
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  runApp(const SanginForooshApp());
}

class SanginForooshApp extends StatelessWidget {
  const SanginForooshApp({super.key});

  @override
  Widget build(BuildContext context) {
    final apiService = ApiService();

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => ThemeProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => AuthProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => MachineryProvider(apiService: apiService),
        ),
        ChangeNotifierProvider(
          create: (context) => AIProvider(AIService(ApiService())),
        ),
        ChangeNotifierProvider(
          create: (context) => CrmProvider(apiService),
        ),
        ChangeNotifierProvider(
          create: (context) => ActiveExhibitionProvider(),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: AppConfig.appName,
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            home: const AuthWrapper(),
            routes: {
                '/test-api': (context) => const TestApiScreen(),
                '/test-exhibition': (context) => const TestExhibitionSystemScreen(),
                '/ai-dashboard': (context) => const AiDashboardScreen(),
                '/financial-anomalies': (context) => const FinancialAnomaliesScreen(),
                '/smart-query': (context) => const SmartQueryScreen(),
                '/ai-agents': (context) => const AIAgentsScreen(agentId: ''),
                '/agent-chat': (context) => const AgentChatScreen(agentId: '', contactId: '1'),
                '/contact-detail': (context) => const ContactDetailScreen(contactId: '1'),
              },
            builder: (context, child) {
              return Directionality(
                textDirection: TextDirection.rtl,
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      await context.read<AuthProvider>().checkAuthStatus();
      // اگر کاربر احراز هویت شده است، نمایشگاه فعال را بارگذاری کنیم
      if (mounted && context.read<AuthProvider>().isAuthenticated) {
        await context.read<ActiveExhibitionProvider>().loadActiveExhibition();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, ActiveExhibitionProvider>(
      builder: (context, authProvider, exhibitionProvider, child) {
        switch (authProvider.state) {
          case AuthState.loading:
          case AuthState.initial:
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          case AuthState.authenticated:
            // اگر کاربر احراز هویت شده است، بررسی کنیم که آیا نمایشگاه فعال دارد یا نه
            if (exhibitionProvider.activeExhibition != null) {
              return const MainShell();
            } else {
              return const ExhibitionSelectionScreen();
            }
          case AuthState.unauthenticated:
          case AuthState.error:
            return const LoginScreen();
        }
      },
    );
  }
}
