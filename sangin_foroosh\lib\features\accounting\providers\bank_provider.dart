import 'package:flutter/material.dart';
import '../models/models.dart';
import '../repositories/accounting_repository.dart';

class BankProvider extends ChangeNotifier {
  final AccountingRepository _repository;

  BankProvider() : _repository = AccountingRepository();

  List<Bank> _banks = [];
  Bank? _selectedBank;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _pagination = {};

  // Getters
  List<Bank> get banks => _banks;
  Bank? get selectedBank => _selectedBank;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic> get pagination => _pagination;
  bool get hasError => _error != null;
  bool get isEmpty => _banks.isEmpty && !_isLoading;

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error state
  void _setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  // Load banks
  Future<void> loadBanks({
    int page = 1,
    int perPage = 20,
    String? search,
    bool refresh = false,
  }) async {
    try {
      if (refresh || page == 1) {
        _setLoading(true);
        _error = null;
      }

      final result = await _repository.getBanks(
        page: page,
        perPage: perPage,
        search: search,
      );

      final List<Bank> banks = List<Bank>.from(result['banks']);
      if (page == 1 || refresh) {
        _banks = banks;
      } else {
        _banks.addAll(banks);
      }
      
      _pagination = result['pagination'];
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری بانک‌ها: ${e.toString()}');
    }
  }

  // Load bank by ID
  Future<void> loadBank(int id) async {
    try {
      _setLoading(true);
      _error = null;

      _selectedBank = await _repository.getBank(id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری بانک: ${e.toString()}');
    }
  }

  // Create bank
  Future<bool> createBank(Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final newBank = await _repository.createBank(data);
      _banks.insert(0, newBank);
      _selectedBank = newBank;
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ایجاد بانک: ${e.toString()}');
      return false;
    }
  }

  // Update bank
  Future<bool> updateBank(int id, Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final updatedBank = await _repository.updateBank(id, data);
      
      final index = _banks.indexWhere((bank) => bank.id == id);
      if (index != -1) {
        _banks[index] = updatedBank;
      }
      
      if (_selectedBank?.id == id) {
        _selectedBank = updatedBank;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ویرایش بانک: ${e.toString()}');
      return false;
    }
  }

  // Delete bank
  Future<bool> deleteBank(int id) async {
    try {
      _setLoading(true);
      _error = null;

      await _repository.deleteBank(id);
      
      _banks.removeWhere((bank) => bank.id == id);
      
      if (_selectedBank?.id == id) {
        _selectedBank = null;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در حذف بانک: ${e.toString()}');
      return false;
    }
  }

  // Select bank
  void selectBank(Bank? bank) {
    _selectedBank = bank;
    notifyListeners();
  }

  // Clear selected bank
  void clearSelection() {
    _selectedBank = null;
    notifyListeners();
  }

  // Search banks
  Future<void> searchBanks(String query) async {
    await loadBanks(search: query, refresh: true);
  }

  // Refresh banks
  Future<void> refresh() async {
    await loadBanks(refresh: true);
  }

  // Load more banks (pagination)
  Future<void> loadMore() async {
    if (_isLoading) return;
    
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    
    if (currentPage < lastPage) {
      await loadBanks(page: currentPage + 1);
    }
  }

  // Check if can load more
  bool get canLoadMore {
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    return currentPage < lastPage && !_isLoading;
  }

  // Get bank by ID from loaded list
  Bank? getBankById(int id) {
    try {
      return _banks.firstWhere((bank) => bank.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get bank by code
  Bank? getBankByCode(String code) {
    try {
      return _banks.firstWhere((bank) => bank.code == code);
    } catch (e) {
      return null;
    }
  }

  // Get bank by SWIFT code
  Bank? getBankBySwiftCode(String swiftCode) {
    try {
      return _banks.firstWhere((bank) => bank.swiftCode == swiftCode);
    } catch (e) {
      return null;
    }
  }

  // Get banks for dropdown
  List<Map<String, dynamic>> getBanksForDropdown() {
    return _banks.map((bank) => {
      'value': bank.id,
      'label': bank.name,
      'code': bank.code,
      'swiftCode': bank.swiftCode,
    }).toList();
  }


}