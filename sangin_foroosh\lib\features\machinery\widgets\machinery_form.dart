import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sangin_foroosh/features/machinery/models/machinery.dart';
import 'package:sangin_foroosh/features/machinery/providers/machinery_provider.dart';

class MachineryForm extends StatefulWidget {
  final Machinery? machinery;

  const MachineryForm({Key? key, this.machinery}) : super(key: key);

  @override
  _MachineryFormState createState() => _MachineryFormState();
}

class _MachineryFormState extends State<MachineryForm> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _modelController;
  late TextEditingController _serialNumberController;
  late TextEditingController _descriptionController;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.machinery?.name ?? '');
    _modelController = TextEditingController(text: widget.machinery?.model ?? '');
    _serialNumberController = TextEditingController(text: widget.machinery?.serialNumber ?? '');
    _descriptionController = TextEditingController(text: widget.machinery?.description ?? '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _modelController.dispose();
    _serialNumberController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      final data = {
        'name': _nameController.text,
        'model': _modelController.text,
        'serial_number': _serialNumberController.text,
        'description': _descriptionController.text,
        // Add other fields as necessary
      };

      final provider = context.read<MachineryProvider>();
      bool success = false;

      try {
        if (widget.machinery == null) {
          success = await provider.createMachinery(data);
        } else {
          success = await provider.updateMachinery(widget.machinery!.id, data);
        }

        if (mounted) {
          if (success) {
            Navigator.of(context).pop(true); // Return true on success
          } else {
            _showErrorDialog(provider.error ?? 'یک خطای ناشناخته رخ داد.');
          }
        }
      } catch (e) {
        if (mounted) {
          _showErrorDialog(e.toString());
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text('خطا'),
        content: Text(message),
        actions: <Widget>[
          TextButton(
            child: Text('باشه'),
            onPressed: () {
              Navigator.of(ctx).pop();
            },
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(labelText: 'نام دستگاه'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'لطفا نام دستگاه را وارد کنید';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _modelController,
                decoration: InputDecoration(labelText: 'مدل'),
                 validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'لطفا مدل را وارد کنید';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _serialNumberController,
                decoration: InputDecoration(labelText: 'شماره سریال'),
                 validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'لطفا شماره سریال را وارد کنید';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(labelText: 'توضیحات'),
                maxLines: 3,
              ),
              SizedBox(height: 20),
              _isLoading
                  ? CircularProgressIndicator()
                  : ElevatedButton(
                      onPressed: _submit,
                      child: Text(widget.machinery == null ? 'ایجاد' : 'ذخیره'),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}