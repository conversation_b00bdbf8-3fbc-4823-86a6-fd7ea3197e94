class Machinery {
  final int id;
  final String name;
  final String category;
  final String status;
  final String description;
  final double dailyRate;
  final double weeklyRate;
  final double monthlyRate;
  final String manufacturer;
  final String model;
  final int manufacturingYear;
  final String serialNumber;
  final List<String> images;
  final Map<String, dynamic> specifications;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? currentLocation;
  final String? ownerName;
  final String? ownerPhone;
  final String? chassisNumber;
  final String? engineNumber;
  final int? manufactureYear;
  final double? enginePower;
  final double? workingHours;
  final double? capacity;
  final String? color;
  final double? sellingPrice;
  final int? exhibitionId;
  final String? plateNumber;

  Machinery({
    required this.id,
    required this.name,
    required this.category,
    required this.status,
    required this.description,
    required this.dailyRate,
    required this.weeklyRate,
    required this.monthlyRate,
    required this.manufacturer,
    required this.model,
    required this.manufacturingYear,
    required this.serialNumber,
    required this.images,
    required this.specifications,
    required this.createdAt,
    required this.updatedAt,
    this.currentLocation,
    this.ownerName,
    this.ownerPhone,
    this.chassisNumber,
    this.engineNumber,
    this.manufactureYear,
    this.enginePower,
    this.workingHours,
    this.capacity,
    this.color,
    this.sellingPrice,
    this.exhibitionId,
    this.plateNumber,
  });

  factory Machinery.fromJson(Map<String, dynamic> json) {
    return Machinery(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      category: json['category'] ?? '',
      status: json['status'] ?? '',
      description: json['description'] ?? '',
      dailyRate: (json['daily_rate'] ?? 0).toDouble(),
      weeklyRate: (json['weekly_rate'] ?? 0).toDouble(),
      monthlyRate: (json['monthly_rate'] ?? 0).toDouble(),
      manufacturer: json['manufacturer'] ?? '',
      model: json['model'] ?? '',
      manufacturingYear: json['manufacturing_year'] ?? 0,
      serialNumber: json['serial_number'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      specifications: Map<String, dynamic>.from(json['specifications'] ?? {}),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      currentLocation: json['current_location'],
      ownerName: json['owner_name'],
      ownerPhone: json['owner_phone'],
      chassisNumber: json['chassis_number'],
      engineNumber: json['engine_number'],
      manufactureYear: json['manufacture_year'],
      enginePower: json['engine_power']?.toDouble(),
      workingHours: json['working_hours']?.toDouble(),
      capacity: json['capacity']?.toDouble(),
      color: json['color'],
      sellingPrice: json['selling_price']?.toDouble(),
      exhibitionId: json['exhibition_id'],
      plateNumber: json['plate_number'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'status': status,
      'description': description,
      'daily_rate': dailyRate,
      'weekly_rate': weeklyRate,
      'monthly_rate': monthlyRate,
      'manufacturer': manufacturer,
      'model': model,
      'manufacturing_year': manufacturingYear,
      'serial_number': serialNumber,
      'images': images,
      'specifications': specifications,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'current_location': currentLocation,
      'owner_name': ownerName,
      'owner_phone': ownerPhone,
      'chassis_number': chassisNumber,
      'engine_number': engineNumber,
      'manufacture_year': manufactureYear,
      'engine_power': enginePower,
      'working_hours': workingHours,
      'capacity': capacity,
      'color': color,
      'selling_price': sellingPrice,
      'exhibition_id': exhibitionId,
      'plate_number': plateNumber,
    };
  }

  String get statusText {
    switch (status.toLowerCase()) {
      case 'available':
        return 'فعال';
      case 'rented':
        return 'اجاره‌ای';
      case 'maintenance':
        return 'تعمیر';
      case 'inactive':
        return 'غیرفعال';
      default:
        return status;
    }
  }

  String get primaryImage {
    return images.isNotEmpty ? images.first : '';
  }

  String get formattedDailyRate {
    return '${dailyRate.toStringAsFixed(0)} تومان';
  }

  String get formattedWeeklyRate {
    return '${weeklyRate.toStringAsFixed(0)} تومان';
  }

  String get formattedMonthlyRate {
    return '${monthlyRate.toStringAsFixed(0)} تومان';
  }

  bool get isAvailable {
    return status.toLowerCase() == 'available';
  }

  bool get isRented {
    return status.toLowerCase() == 'rented';
  }

  bool get isInMaintenance {
    return status.toLowerCase() == 'maintenance';
  }

  bool get isInactive {
    return status.toLowerCase() == 'inactive';
  }
}

class MachineryStatistics {
  final int total;
  final int available;
  final int rented;
  final int maintenance;
  final int inactive;
  final double totalValue;
  final double monthlyRevenue;

  MachineryStatistics({
    required this.total,
    required this.available,
    required this.rented,
    required this.maintenance,
    required this.inactive,
    required this.totalValue,
    required this.monthlyRevenue,
  });

  factory MachineryStatistics.fromJson(Map<String, dynamic> json) {
    return MachineryStatistics(
      total: json['total'] ?? 0,
      available: json['available'] ?? 0,
      rented: json['rented'] ?? 0,
      maintenance: json['maintenance'] ?? 0,
      inactive: json['inactive'] ?? 0,
      totalValue: (json['total_value'] ?? 0).toDouble(),
      monthlyRevenue: (json['monthly_revenue'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'available': available,
      'rented': rented,
      'maintenance': maintenance,
      'inactive': inactive,
      'total_value': totalValue,
      'monthly_revenue': monthlyRevenue,
    };
  }

  String get formattedTotalValue {
    return '${totalValue.toStringAsFixed(0)} تومان';
  }

  String get formattedMonthlyRevenue {
    return '${monthlyRevenue.toStringAsFixed(0)} تومان';
  }



  double get utilizationRate {
    if (total == 0) return 0.0;
    return (rented / total) * 100;
  }

  String get formattedUtilizationRate {
    return '${utilizationRate.toStringAsFixed(1)}%';
  }
}