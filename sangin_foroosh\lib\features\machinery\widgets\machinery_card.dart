import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../shared/models/machinery.dart';

class MachineryCard extends StatelessWidget {
  final Machinery machinery;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final bool isFavorite;

  const MachineryCard({
    super.key,
    required this.machinery,
    this.onTap,
    this.onFavorite,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImageSection(),
            _buildContentSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Stack(
      children: [
        Container(
          height: 200,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
            color: AppColors.surfaceVariant,
          ),
          child: machinery.primaryImageUrl != null
              ? ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  child: Image.network(
                    machinery.primaryImageUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildPlaceholderImage();
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return _buildLoadingImage();
                    },
                  ),
                )
              : _buildPlaceholderImage(),
        ),
        Positioned(
          top: 12,
          right: 12,
          child: _buildStatusBadge(),
        ),
        if (onFavorite != null)
          Positioned(
            top: 12,
            left: 12,
            child: _buildFavoriteButton(),
          ),
        if (machinery.images.length > 1)
          Positioned(
            bottom: 12,
            right: 12,
            child: _buildImageCountBadge(),
          ),
      ],
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        color: AppColors.surfaceVariant,
      ),
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction,
            size: 48,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 8),
          Text(
            'بدون تصویر',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontFamily: 'IranSansX',
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingImage() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        color: AppColors.surfaceVariant,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      ),
    );
  }

  Widget _buildStatusBadge() {
    Color backgroundColor;
    Color textColor = Colors.white;

    switch (machinery.status.toLowerCase()) {
      case 'available':
        backgroundColor = AppColors.success;
        break;
      case 'rented':
        backgroundColor = AppColors.warning;
        break;
      case 'maintenance':
        backgroundColor = AppColors.error;
        break;
      case 'inactive':
        backgroundColor = AppColors.secondary;
        break;
      default:
        backgroundColor = AppColors.secondary;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        machinery.statusDisplayName,
        style: TextStyle(
          color: textColor,
          fontSize: 10,
          fontWeight: FontWeight.w600,
          fontFamily: 'IranSansX',
        ),
      ),
    );
  }

  Widget _buildFavoriteButton() {
    return GestureDetector(
      onTap: onFavorite,
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          shape: BoxShape.circle,
        ),
        child: Icon(
          isFavorite ? Icons.favorite : Icons.favorite_border,
          color: isFavorite ? AppColors.error : AppColors.textSecondary,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildImageCountBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.photo_library,
            color: Colors.white,
            size: 12,
          ),
          const SizedBox(width: 2),
          Text(
            machinery.images.length.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontFamily: 'IranSansX',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitleAndCategory(),
          const SizedBox(height: 8),
          _buildDescription(),
          const SizedBox(height: 12),
          _buildSpecifications(),
          const SizedBox(height: 12),
          _buildPricing(),
          const SizedBox(height: 8),
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildTitleAndCategory() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                machinery.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'IranSansX',
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                machinery.type ?? 'نامشخص',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                  fontFamily: 'IranSansX',
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            machinery.createdAt.year.toString(),
            style: const TextStyle(
              fontSize: 10,
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
              fontFamily: 'IranSansX',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescription() {
    return Text(
      machinery.description,
      style: const TextStyle(
        fontSize: 13,
        color: AppColors.textSecondary,
        fontFamily: 'IranSansX',
        height: 1.4,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSpecifications() {
    return Row(
      children: [
        _buildSpecItem(Icons.business, machinery.manufacturer),
        const SizedBox(width: 16),
        _buildSpecItem(Icons.model_training, machinery.model),
      ],
    );
  }

  Widget _buildSpecItem(IconData icon, String text) {
    return Expanded(
      child: Row(
        children: [
          Icon(
            icon,
            size: 14,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 11,
                color: AppColors.textSecondary,
                fontFamily: 'IranSansX',
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPricing() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildPriceItem('روزانه', machinery.formattedDailyRate),
          ),
          Container(
            width: 1,
            height: 20,
            color: AppColors.border,
          ),
          Expanded(
            child: _buildPriceItem('هفتگی', machinery.formattedWeeklyRate),
          ),
          Container(
            width: 1,
            height: 20,
            color: AppColors.border,
          ),
          Expanded(
            child: _buildPriceItem('ماهانه', machinery.formattedMonthlyRate),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceItem(String label, String price) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: AppColors.textSecondary,
            fontFamily: 'IranSansX',
          ),
        ),
        const SizedBox(height: 2),
        Text(
          price,
          style: const TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
            fontFamily: 'IranSansX',
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (machinery.currentLocation != null)
          Row(
            children: [
              const Icon(
                Icons.location_on,
                size: 14,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: 4),
              Text(
                machinery.currentLocation!,
                style: const TextStyle(
                  fontSize: 11,
                  color: AppColors.textSecondary,
                  fontFamily: 'IranSansX',
                ),
              ),
            ],
          ),
        const Spacer(),
        Text(
          'کد: ${machinery.serialNumber}',
          style: const TextStyle(
            fontSize: 10,
            color: AppColors.textSecondary,
            fontFamily: 'IranSansX',
          ),
        ),
      ],
    );
  }
}