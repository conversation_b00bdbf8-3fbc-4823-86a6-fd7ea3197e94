import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:intl/intl.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';

import '../models/machinery.dart';
import '../../../shared/models/machinery_event.dart';
import '../providers/machinery_provider.dart';
import '../providers/machinery_event_provider.dart';
import '../../../core/utils/jalali_formatter.dart';
import '../../../core/constants/app_config.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../ai/providers/ai_provider.dart';
import '../../ai/widgets/price_prediction_dialog.dart';
import '../../ai/services/ai_service.dart';

class MachineryDetailScreen extends StatefulWidget {
  final dynamic machineryId;

  const MachineryDetailScreen({Key? key, required this.machineryId}) : super(key: key);

  @override
  State<MachineryDetailScreen> createState() => _MachineryDetailScreenState();
}

class _MachineryDetailScreenState extends State<MachineryDetailScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Machinery? _machinery;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadMachineryDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMachineryDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final machineryProvider = Provider.of<MachineryProvider>(context, listen: false);
      final machinery = await machineryProvider.getMachinery(widget.machineryId);
      
      if (machinery != null) {
        setState(() {
          _machinery = machinery;
          _isLoading = false;
        });
        
        // بارگذاری رویدادهای ماشین
        final eventProvider = Provider.of<MachineryEventProvider>(context, listen: false);
        await eventProvider.loadMachineryEvents(refresh: true);
      } else {
        setState(() {
          _error = 'ماشین مورد نظر یافت نشد';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_machinery?.name ?? 'جزئیات ماشین'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // هدایت به صفحه ویرایش
              Navigator.pushNamed(
                context, 
                '/machinery/edit', 
                arguments: _machinery
              ).then((_) => _loadMachineryDetails());
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'اطلاعات کلی', icon: Icon(Icons.info_outline)),
            Tab(text: 'گالری تصاویر', icon: Icon(Icons.photo_library)),
            Tab(text: 'تاریخچه وقایع', icon: Icon(Icons.history)),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _error != null
              ? AppErrorWidget(message: _error!, onRetry: _loadMachineryDetails)
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildGeneralInfoTab(),
                    _buildGalleryTab(),
                    _buildEventsTab(),
                  ],
                ),
      floatingActionButton: _tabController.index == 2
          ? FloatingActionButton(
              onPressed: () {
                // هدایت به صفحه افزودن رویداد جدید
                Navigator.pushNamed(
                  context, 
                  '/machinery/event/add', 
                  arguments: {'machineryId': widget.machineryId}
                ).then((_) {
                  // بارگذاری مجدد رویدادها
                  if (mounted) {
                    Provider.of<MachineryEventProvider>(context, listen: false)
                        .loadMachineryEvents(refresh: true);
                  }
                });
              },
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildGeneralInfoTab() {
    if (_machinery == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // کارت اطلاعات اصلی
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'اطلاعات اصلی',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const Divider(),
                  _infoRow('نام', _machinery!.name),
                  _infoRow('برند', _machinery!.brand ?? '-'),
                  _infoRow('مدل', _machinery!.model ?? '-'),
                  _infoRow('نوع', _machinery!.type ?? '-'),
                  _infoRow('شماره سریال', _machinery!.serialNumber),
                  _infoRow('وضعیت', _machinery!.statusDisplayName),
                  _infoRow('موقعیت', _machinery!.location ?? '-'),
                  _infoRow('شرایط', _machinery!.condition ?? '-'),
                ],
              ),
            ),
          ),

          // کارت اطلاعات فنی
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'اطلاعات فنی',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const Divider(),
                  _infoRow('قیمت خرید', _machinery!.purchasePrice != null ? '${_machinery!.purchasePrice} تومان' : '-'),
                  _infoRow('تاریخ خرید', _machinery!.purchaseDate != null ? JalaliFormatter.formatDate(_machinery!.purchaseDate!) : '-'),
                  if (_machinery!.specifications != null) ..._buildSpecifications(),
                ],
              ),
            ),
          ),

          // کارت اطلاعات مالی
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'اطلاعات مالی',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      ElevatedButton.icon(
                        onPressed: () => _showPricePredictionDialog(),
                        icon: const Icon(TablerIcons.wand, size: 18),
                        label: const Text('تخمین قیمت بازار'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const Divider(),
                  _infoRow('قیمت خرید', _machinery!.purchasePrice != null ? _formatPrice(_machinery!.purchasePrice!) : '-'),
                  // قیمت فروش در مدل جدید موجود نیست
                  _infoRow('تاریخ خرید', _machinery!.purchaseDate != null ? JalaliFormatter.formatDate(_machinery!.purchaseDate!) : '-'),
                ],
              ),
            ),
          ),

          // کارت توضیحات
          if (_machinery!.description != null && _machinery!.description!.isNotEmpty)
            Card(
              elevation: 2,
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'توضیحات',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const Divider(),
                    Text(_machinery!.description!),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _infoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatPrice(num price) {
    final formatter = NumberFormat('#,###', 'fa_IR');
    return '${formatter.format(price)} تومان';
  }

  Widget _buildGalleryTab() {
    if (_machinery == null || _machinery!.images == null || _machinery!.images!.isEmpty) {
      return const Center(child: Text('تصویری برای نمایش وجود ندارد'));
    }

    return GridView.builder(
      padding: const EdgeInsets.all(8.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
      ),
      itemCount: _machinery!.images!.length,
      itemBuilder: (context, index) {
        final image = _machinery!.images![index];
        return GestureDetector(
          onTap: () => _openGallery(index),
          child: Card(
            clipBehavior: Clip.antiAlias,
            child: CachedNetworkImage(
              imageUrl: '${AppConfig.baseUrl}${image.url}',
              fit: BoxFit.cover,
              placeholder: (context, url) => const Center(child: CircularProgressIndicator()),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          ),
        );
      },
    );
  }

  void _openGallery(int initialIndex) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('گالری تصاویر'),
            backgroundColor: Colors.black,
          ),
          body: Container(
            color: Colors.black,
            child: PhotoViewGallery.builder(
              itemCount: _machinery!.images!.length,
              builder: (context, index) {
                final image = _machinery!.images![index];
                return PhotoViewGalleryPageOptions(
                  imageProvider: CachedNetworkImageProvider(
                    '${AppConfig.baseUrl}${image.url}',
                  ),
                  minScale: PhotoViewComputedScale.contained,
                  maxScale: PhotoViewComputedScale.covered * 2,
                );
              },
              scrollPhysics: const BouncingScrollPhysics(),
              backgroundDecoration: const BoxDecoration(color: Colors.black),
              pageController: PageController(initialPage: initialIndex),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEventsTab() {
    return Consumer<MachineryEventProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const LoadingWidget();
        }

        if (provider.error != null) {
          return AppErrorWidget(
            error: provider.error!,
            onRetry: () => provider.loadMachineryEvents(
              refresh: true,
            ),
          );
        }

        if (provider.events.isEmpty) {
          return const Center(child: Text('هیچ رویدادی ثبت نشده است'));
        }

        // مرتب‌سازی رویدادها بر اساس تاریخ (جدیدترین در بالا)
        final events = List<MachineryEvent>.from(provider.events)
          ..sort((a, b) => b.eventDate.compareTo(a.eventDate));

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: events.length,
          itemBuilder: (context, index) {
            final event = events[index];
            final isFirst = index == 0;
            final isLast = index == events.length - 1;

            return TimelineTile(
              alignment: TimelineAlign.manual,
              lineXY: 0.2,
              isFirst: isFirst,
              isLast: isLast,
              indicatorStyle: IndicatorStyle(
                width: 30,
                height: 30,
                indicator: _getEventIndicator(event.type),
              ),
              beforeLineStyle: const LineStyle(
                color: Colors.grey,
                thickness: 2,
              ),
              endChild: _buildEventCard(event),
              startChild: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  JalaliFormatter.formatDate(event.eventDate!),
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _getEventIndicator(String type) {
    IconData iconData;
    Color color;

    // استفاده از متدهای کمکی مدل MachineryEvent
    final event = MachineryEvent(type: type);
    
    if (event.isPurchase) {
      iconData = Icons.shopping_cart;
      color = Colors.green;
    } else if (event.isSale) {
      iconData = Icons.monetization_on;
      color = Colors.blue;
    } else if (event.isRepair) {
      iconData = Icons.build;
      color = Colors.orange;
    } else if (event.isMaintenance) {
      iconData = Icons.settings;
      color = Colors.purple;
    } else if (event.isInspection) {
      iconData = Icons.search;
      color = Colors.teal;
    } else {
      iconData = Icons.event_note;
      color = Colors.grey;
    }

    return Container(
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
      child: Icon(
        iconData,
        color: Colors.white,
        size: 16,
      ),
    );
  }

  Widget _buildEventCard(MachineryEvent event) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  event.title,
                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                Text(
                  event.typeDisplayName,
                  style: TextStyle(
                    color: _getEventColor(event.type),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            if (event.cost != null) ...[  
              const SizedBox(height: 8),
              Text('هزینه: ${event.formattedCost}'),
            ],
            if (event.location != null && event.location!.isNotEmpty) ...[  
              const SizedBox(height: 4),
              Text('مکان: ${event.location}'),
            ],
            if (event.relatedUserId != null) ...[  
              const SizedBox(height: 4),
              Text('کاربر مرتبط: ${event.relatedUser?['name'] ?? 'کاربر ${event.relatedUserId}'}'),
            ],
            if (event.description != null && event.description!.isNotEmpty) ...[  
              const SizedBox(height: 8),
              const Divider(),
              Text(event.description!),
            ],
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, size: 20),
                  onPressed: () {
                    // هدایت به صفحه ویرایش رویداد
                    Navigator.pushNamed(
                      context, 
                      '/machinery/event/edit', 
                      arguments: {'event': event}
                    ).then((_) {
                      // بارگذاری مجدد رویدادها
                      if (mounted) {
                        Provider.of<MachineryEventProvider>(context, listen: false)
                            .loadMachineryEvents(refresh: true);
                      }
                    });
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                  onPressed: () => _confirmDeleteEvent(event),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getEventColor(String type) {
    // استفاده از متدهای کمکی مدل MachineryEvent
    final event = MachineryEvent(type: type);
    
    if (event.isPurchase) return Colors.green;
    if (event.isSale) return Colors.blue;
    if (event.isRepair) return Colors.orange;
    if (event.isMaintenance) return Colors.purple;
    if (event.isInspection) return Colors.teal;
    return Colors.grey;
  }

  Future<void> _confirmDeleteEvent(MachineryEvent event) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف رویداد'),
        content: Text('آیا از حذف رویداد "${event.title}" اطمینان دارید؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('انصراف'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final provider = Provider.of<MachineryEventProvider>(context, listen: false);
      final success = await provider.deleteMachineryEvent(event.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success ? 'رویداد با موفقیت حذف شد' : 'خطا در حذف رویداد',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }

  void _showPricePredictionDialog() {
    if (_machinery == null) return;
    
    showDialog(
      context: context,
      builder: (context) => ChangeNotifierProvider(
        create: (context) => AIProvider(AIService()),
        child: PricePredictionDialog(machinery: _machinery!),
      ),
    );
  }

  List<Widget> _buildSpecifications() {
    if (_machinery!.specifications == null) return [];

    return _machinery!.specifications!.entries.map((entry) {
      return _infoRow(entry.key, entry.value?.toString() ?? '-');
    }).toList();
  }

}