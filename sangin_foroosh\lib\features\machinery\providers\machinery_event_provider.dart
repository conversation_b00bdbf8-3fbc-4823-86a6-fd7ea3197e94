import 'package:flutter/material.dart';
import '../../../shared/models/machinery_event.dart';
import '../repositories/machinery_repository.dart';

class MachineryEventProvider with ChangeNotifier {
  final MachineryRepository _machineryRepository;
  final int _machineryId;

  MachineryEventProvider({
    required MachineryRepository machineryRepository,
    required int machineryId,
  }) : _machineryRepository = machineryRepository,
       _machineryId = machineryId;

  List<MachineryEvent> _events = [];
  bool _isLoading = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMorePages = true;
  
  // گترها
  List<MachineryEvent> get events => _events;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMorePages => _hasMorePages;
  
  /// بارگذاری لیست رویدادهای ماشین‌آلات
  Future<void> loadMachineryEvents({
    bool refresh = false,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
    String? sortBy,
    String? sortOrder,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMorePages = true;
      _events = [];
    }

    if (!_hasMorePages || _isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final loadedEvents = await _machineryRepository.getMachineryEvents(
        machineryId: _machineryId,
        page: _currentPage,
        type: type,
        startDate: startDate,
        endDate: endDate,
        sortBy: sortBy,
        sortOrder: sortOrder,
      );

      if (refresh) {
        _events = loadedEvents;
      } else {
        _events.addAll(loadedEvents);
      }

      // Assuming the repository returns an empty list when there are no more pages
      if (loadedEvents.isNotEmpty) {
        _currentPage++;
        _hasMorePages = true;
      } else {
        _hasMorePages = false;
      }
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  /// بارگذاری اطلاعات یک رویداد خاص
  Future<MachineryEvent?> getMachineryEvent(int eventId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final event = await _machineryRepository.getMachineryEvent(eventId);

      _isLoading = false;
      notifyListeners();
      return event;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
  
  /// ایجاد رویداد جدید
  Future<MachineryEvent?> createMachineryEvent(MachineryEvent eventData) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final event = await _machineryRepository.createMachineryEvent(eventData);

      // اضافه کردن به لیست
      _events.insert(0, event);

      _isLoading = false;
      notifyListeners();
      return event;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
  
  /// به‌روزرسانی رویداد
  Future<MachineryEvent?> updateMachineryEvent(MachineryEvent eventData) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final updatedEvent = await _machineryRepository.updateMachineryEvent(eventData);

      // به‌روزرسانی در لیست
      final index = _events.indexWhere((e) => e.id == updatedEvent.id);
      if (index != -1) {
        _events[index] = updatedEvent;
      }

      _isLoading = false;
      notifyListeners();
      return updatedEvent;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
  
  /// حذف رویداد
  Future<bool> deleteMachineryEvent(int eventId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _machineryRepository.deleteMachineryEvent(eventId);

      // حذف از لیست
      _events.removeWhere((e) => e.id == eventId);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
  
  /// پاک کردن خطا
  void clearError() {
    _error = null;
    notifyListeners();
  }
}