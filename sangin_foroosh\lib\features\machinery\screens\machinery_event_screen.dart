import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timeline_tile/timeline_tile.dart';

import '../../../shared/models/machinery_event.dart';
import '../models/machinery.dart';
import '../providers/machinery_event_provider.dart';
import '../providers/machinery_provider.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../core/utils/jalali_formatter.dart';

class MachineryEventScreen extends StatefulWidget {
  final int machineryId;

  const MachineryEventScreen({Key? key, required this.machineryId}) : super(key: key);

  @override
  State<MachineryEventScreen> createState() => _MachineryEventScreenState();
}

class _MachineryEventScreenState extends State<MachineryEventScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;
  Machinery? _machinery;

  @override
  void initState() {
    super.initState();
    _loadData();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      _loadMoreEvents();
    }
  }

  Future<void> _loadData() async {
    final machineryProvider = Provider.of<MachineryProvider>(context, listen: false);
    final eventProvider = Provider.of<MachineryEventProvider>(context, listen: false);
    
    try {
      // بارگذاری اطلاعات ماشین
      final machinery = await machineryProvider.getMachinery(widget.machineryId);
      if (mounted) {
        setState(() {
          _machinery = machinery;
        });
      }
      
      // بارگذاری رویدادهای ماشین
      await eventProvider.loadMachineryEvents(refresh: true);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطا در بارگذاری اطلاعات: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _loadMoreEvents() async {
    final provider = Provider.of<MachineryEventProvider>(context, listen: false);
    if (!provider.isLoading && provider.hasMorePages) {
      await provider.loadMachineryEvents(widget.machineryId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_machinery?.name ?? 'رویدادهای ماشین'),
      ),
      body: Consumer<MachineryEventProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.events.isEmpty) {
            return const LoadingWidget();
          }

          if (provider.error != null && provider.events.isEmpty) {
            return AppErrorWidget(
              error: provider.error!,
              onRetry: _loadData,
            );
          }

          if (provider.events.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.event_busy, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  const Text('هیچ رویدادی برای این ماشین ثبت نشده است'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _showEventDialog(),
                    child: const Text('افزودن رویداد جدید'),
                  ),
                ],
              ),
            );
          }

          return Stack(
            children: [
              RefreshIndicator(
                onRefresh: _loadData,
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: provider.events.length + (provider.hasMorePages ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == provider.events.length) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final event = provider.events[index];
                    return _buildEventCard(event);
                  },
                ),
              ),
              if (provider.isLoading && provider.events.isNotEmpty)
                const Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: LinearProgressIndicator(),
                ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showEventDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEventCard(MachineryEvent event) {
    Color typeColor;
    IconData typeIcon;

    // تعیین رنگ و آیکون بر اساس نوع رویداد
    switch (event.type) {
      case 'purchase':
        typeColor = Colors.green;
        typeIcon = Icons.shopping_cart;
        break;
      case 'sale':
        typeColor = Colors.blue;
        typeIcon = Icons.monetization_on;
        break;
      case 'maintenance':
        typeColor = Colors.orange;
        typeIcon = Icons.build;
        break;
      case 'repair':
        typeColor = Colors.red;
        typeIcon = Icons.handyman;
        break;
      case 'inspection':
        typeColor = Colors.purple;
        typeIcon = Icons.fact_check;
        break;
      default:
        typeColor = Colors.grey;
        typeIcon = Icons.event_note;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: typeColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(typeIcon, color: typeColor),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event.title,
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        event.typeDisplayName,
                        style: TextStyle(color: typeColor, fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (event.description != null && event.description!.isNotEmpty) ...[  
              const SizedBox(height: 12),
              Text(event.description!),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  'تاریخ: ${JalaliFormatter.formatDate(event.eventDate)}',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
            if (event.cost != null) ...[  
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.attach_money, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'هزینه: ${event.formattedCost}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ],
            if (event.location != null && event.location!.isNotEmpty) ...[  
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.location_on, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'مکان: ${event.location}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ],
            if (event.relatedUserId != null) ...[  
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.person, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    event.relatedUser?.name ?? 'کاربر ${event.relatedUserId}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  onPressed: () => _showEventDialog(event: event),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _confirmDeleteEvent(event),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showEventDialog({MachineryEvent? event}) async {
    // اگر event نال باشد، حالت افزودن است، در غیر این صورت حالت ویرایش
    final isEditing = event != null;
    final formKey = GlobalKey<FormState>();
    final titleController = TextEditingController(text: event?.title ?? '');
    final descriptionController = TextEditingController(text: event?.description ?? '');
    final costController = TextEditingController(text: event?.cost?.toString() ?? '');
    final locationController = TextEditingController(text: event?.location ?? '');
    
    String selectedType = event?.type ?? 'maintenance';
    DateTime selectedDate = event?.eventDate ?? DateTime.now();
    int? selectedUserId = event?.relatedUserId;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'ویرایش رویداد' : 'افزودن رویداد جدید'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String>(
                  value: selectedType,
                  decoration: const InputDecoration(labelText: 'نوع رویداد *'),
                  items: [
                    DropdownMenuItem(value: 'purchase', child: Text('خرید')),
                    DropdownMenuItem(value: 'sale', child: Text('فروش')),
                    DropdownMenuItem(value: 'maintenance', child: Text('نگهداری')),
                    DropdownMenuItem(value: 'repair', child: Text('تعمیر')),
                    DropdownMenuItem(value: 'inspection', child: Text('بازرسی')),
                    DropdownMenuItem(value: 'other', child: Text('سایر')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      selectedType = value;
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'لطفاً نوع رویداد را انتخاب کنید';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: titleController,
                  decoration: const InputDecoration(labelText: 'عنوان رویداد *'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'لطفاً عنوان رویداد را وارد کنید';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: descriptionController,
                  decoration: const InputDecoration(labelText: 'توضیحات'),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: selectedDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                    );
                    if (picked != null) {
                      setState(() {
                        selectedDate = picked;
                      });
                    }
                  },
                  child: InputDecorator(
                    decoration: const InputDecoration(labelText: 'تاریخ رویداد *'),
                    child: Text(
                      JalaliFormatter.formatDate(selectedDate),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: costController,
                  decoration: const InputDecoration(
                    labelText: 'هزینه (تومان)',
                    hintText: 'مثال: 1000000',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: locationController,
                  decoration: const InputDecoration(labelText: 'مکان'),
                ),
                const SizedBox(height: 16),
                // TODO: در نسخه‌های آینده، یک انتخاب‌گر کاربر اضافه شود
                // فعلاً از کاربر فعلی استفاده می‌کنیم
                Text(
                  'کاربر مرتبط: ${event?.relatedUser?.name ?? 'کاربر فعلی'}',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('انصراف'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.pop(context);
                
                setState(() {
                  _isLoading = true;
                });
                
                final provider = Provider.of<MachineryEventProvider>(context, listen: false);
                final eventData = {
                  'machinery_id': widget.machineryId,
                  'type': selectedType,
                  'title': titleController.text,
                  'description': descriptionController.text.isNotEmpty ? descriptionController.text : null,
                  'event_date': selectedDate.toIso8601String(),
                  'cost': costController.text.isNotEmpty ? int.parse(costController.text) : null,
                  'location': locationController.text.isNotEmpty ? locationController.text : null,
                  // در حال حاضر از کاربر فعلی استفاده می‌کنیم
                  // 'related_user_id': selectedUserId,
                };
                
                try {
                  if (isEditing) {
                    await provider.updateMachineryEvent(event!.id, eventData);
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('رویداد با موفقیت به‌روزرسانی شد')),
                      );
                    }
                  } else {
                    await provider.createMachineryEvent(eventData);
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('رویداد با موفقیت ایجاد شد')),
                      );
                    }
                  }
                  
                  // بارگذاری مجدد رویدادها
                  if (mounted) {
                    await provider.loadMachineryEvents(refresh: true);
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطا: ${e.toString()}')),
                    );
                  }
                } finally {
                  setState(() {
                    _isLoading = false;
                  });
                }
              }
            },
            child: Text(isEditing ? 'به‌روزرسانی' : 'افزودن'),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmDeleteEvent(MachineryEvent event) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف رویداد'),
        content: Text('آیا از حذف رویداد "${event.title}" اطمینان دارید؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('انصراف'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      setState(() {
        _isLoading = true;
      });

      try {
        final provider = Provider.of<MachineryEventProvider>(context, listen: false);
        final success = await provider.deleteMachineryEvent(event.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success ? 'رویداد با موفقیت حذف شد' : 'خطا در حذف رویداد',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
          
          // بارگذاری مجدد رویدادها
          if (success) {
            await provider.loadMachineryEvents(widget.machineryId, refresh: true);
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطا: ${e.toString()}')),
          );
        }
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}