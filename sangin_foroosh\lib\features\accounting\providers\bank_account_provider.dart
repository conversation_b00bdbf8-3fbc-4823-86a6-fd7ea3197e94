import 'package:flutter/material.dart';
import '../models/models.dart';
import '../repositories/accounting_repository.dart';

class BankAccountProvider extends ChangeNotifier {
  final AccountingRepository _repository;

  BankAccountProvider() : _repository = AccountingRepository();

  List<BankAccount> _bankAccounts = [];
  BankAccount? _selectedBankAccount;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _pagination = {};
  Map<String, dynamic> _statistics = {};

  // Getters
  List<BankAccount> get bankAccounts => _bankAccounts;
  BankAccount? get selectedBankAccount => _selectedBankAccount;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic> get pagination => _pagination;
  Map<String, dynamic> get statistics => _statistics;
  bool get hasError => _error != null;
  bool get isEmpty => _bankAccounts.isEmpty && !_isLoading;

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error state
  void _setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  // Load bank accounts
  Future<void> loadBankAccounts({
    int page = 1,
    int perPage = 20,
    String? search,
    int? bankId,
    int? chartOfAccountId,
    String? accountType,
    bool? isActive,
    bool refresh = false,
  }) async {
    try {
      if (refresh || page == 1) {
        _setLoading(true);
        _error = null;
      }

      final result = await _repository.getBankAccounts(
        page: page,
        perPage: perPage,
        search: search,
        bankId: bankId,
        accountType: accountType,
        isActive: isActive,
      );

      if (page == 1 || refresh) {
        _bankAccounts = result['accounts'];
      } else {
        _bankAccounts.addAll(result['accounts']);
      }
      
      _pagination = result['pagination'];
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری حساب‌های بانکی: ${e.toString()}');
    }
  }

  // Load bank account by ID
  Future<void> loadBankAccount(int id) async {
    try {
      _setLoading(true);
      _error = null;

      _selectedBankAccount = await _repository.getBankAccount(id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری حساب بانکی: ${e.toString()}');
    }
  }

  // Create bank account
  Future<bool> createBankAccount(Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final newAccount = await _repository.createBankAccount(data);
      _bankAccounts.insert(0, newAccount);
      _selectedBankAccount = newAccount;
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ایجاد حساب بانکی: ${e.toString()}');
      return false;
    }
  }

  // Update bank account
  Future<bool> updateBankAccount(int id, Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final updatedAccount = await _repository.updateBankAccount(id, data);
      
      final index = _bankAccounts.indexWhere((acc) => acc.id == id);
      if (index != -1) {
        _bankAccounts[index] = updatedAccount;
      }
      
      if (_selectedBankAccount?.id == id) {
        _selectedBankAccount = updatedAccount;
      }
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ویرایش حساب بانکی: ${e.toString()}');
      return false;
    }
  }

  // Delete bank account
  Future<bool> deleteBankAccount(int id) async {
    try {
      _setLoading(true);
      _error = null;

      await _repository.deleteBankAccount(id);
      
      _bankAccounts.removeWhere((acc) => acc.id == id);
      
      if (_selectedBankAccount?.id == id) {
        _selectedBankAccount = null;
      }
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در حذف حساب بانکی: ${e.toString()}');
      return false;
    }
  }

  // Activate/Deactivate bank account
  Future<bool> toggleBankAccountStatus(int id) async {
    try {
      final account = getBankAccountById(id);
      if (account == null) return false;

      return await updateBankAccount(id, {'is_active': !account.isActive});
    } catch (e) {
      _setError('خطا در تغییر وضعیت حساب بانکی: ${e.toString()}');
      return false;
    }
  }

  // Calculate statistics
  void _calculateStatistics() {
    if (_bankAccounts.isEmpty) {
      _statistics = {};
      return;
    }

    final totalAccounts = _bankAccounts.length;
    final activeAccounts = _bankAccounts.where((acc) => acc.isActive).length;
    final inactiveAccounts = totalAccounts - activeAccounts;
    
    double totalBalance = 0;
    double activeBalance = 0;
    
    final accountTypeCount = <String, int>{};
    
    for (final account in _bankAccounts) {
      totalBalance += account.balance;
      if (account.isActive) {
        activeBalance += account.balance;
      }
      
      accountTypeCount[account.accountType] = (accountTypeCount[account.accountType] ?? 0) + 1;
    }

    _statistics = {
      'total_accounts': totalAccounts,
      'active_accounts': activeAccounts,
      'inactive_accounts': inactiveAccounts,
      'total_balance': totalBalance,
      'active_balance': activeBalance,
      'account_type_count': accountTypeCount,
    };
  }

  // Get accounts by bank
  List<BankAccount> getAccountsByBank(int bankId) {
    return _bankAccounts.where((acc) => acc.bankId == bankId).toList();
  }

  // Get accounts by type
  List<BankAccount> getAccountsByType(String accountType) {
    return _bankAccounts.where((acc) => acc.accountType == accountType).toList();
  }

  // Get active accounts
  List<BankAccount> getActiveAccounts() {
    return _bankAccounts.where((acc) => acc.isActive).toList();
  }

  // Get inactive accounts
  List<BankAccount> getInactiveAccounts() {
    return _bankAccounts.where((acc) => !acc.isActive).toList();
  }

  // Get accounts with positive balance
  List<BankAccount> getAccountsWithPositiveBalance() {
    return _bankAccounts.where((acc) => acc.balance > 0).toList();
  }

  // Get accounts with negative balance
  List<BankAccount> getAccountsWithNegativeBalance() {
    return _bankAccounts.where((acc) => acc.balance < 0).toList();
  }

  // Select bank account
  void selectBankAccount(BankAccount? account) {
    _selectedBankAccount = account;
    notifyListeners();
  }

  // Clear selected bank account
  void clearSelection() {
    _selectedBankAccount = null;
    notifyListeners();
  }

  // Search bank accounts
  Future<void> searchBankAccounts(String query) async {
    await loadBankAccounts(search: query, refresh: true);
  }

  // Filter by bank
  Future<void> filterByBank(int bankId) async {
    await loadBankAccounts(bankId: bankId, refresh: true);
  }

  // Filter by chart of account
  Future<void> filterByChartOfAccount(int chartOfAccountId) async {
    await loadBankAccounts(chartOfAccountId: chartOfAccountId, refresh: true);
  }

  // Filter by account type
  Future<void> filterByAccountType(String accountType) async {
    await loadBankAccounts(accountType: accountType, refresh: true);
  }

  // Filter by active status
  Future<void> filterByActiveStatus(bool isActive) async {
    await loadBankAccounts(isActive: isActive, refresh: true);
  }

  // Refresh bank accounts
  Future<void> refresh() async {
    await loadBankAccounts(refresh: true);
  }

  // Load more bank accounts (pagination)
  Future<void> loadMore() async {
    if (_isLoading) return;
    
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    
    if (currentPage < lastPage) {
      await loadBankAccounts(page: currentPage + 1);
    }
  }

  // Check if can load more
  bool get canLoadMore {
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    return currentPage < lastPage && !_isLoading;
  }

  // Get bank account by ID from loaded list
  BankAccount? getBankAccountById(int id) {
    try {
      return _bankAccounts.firstWhere((acc) => acc.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get bank account by account number
  BankAccount? getBankAccountByNumber(String accountNumber) {
    try {
      return _bankAccounts.firstWhere((acc) => acc.accountNumber == accountNumber);
    } catch (e) {
      return null;
    }
  }

  // Get bank account by IBAN
  BankAccount? getBankAccountByIban(String iban) {
    try {
      return _bankAccounts.firstWhere((acc) => acc.iban == iban);
    } catch (e) {
      return null;
    }
  }

  // Get bank accounts for dropdown
  List<Map<String, dynamic>> getBankAccountsForDropdown({bool activeOnly = true}) {
    final accounts = activeOnly ? getActiveAccounts() : _bankAccounts;
    return accounts.map((account) => {
      'value': account.id,
      'label': '${account.bankName} - ${account.accountNumber}',
      'accountNumber': account.accountNumber,
      'iban': account.iban,
      'balance': account.balance,
      'formattedBalance': account.formattedBalance,
    }).toList();
  }


}