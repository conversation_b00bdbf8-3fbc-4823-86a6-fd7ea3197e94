import 'package:flutter/foundation.dart';
import '../models/accounting_document.dart';
import '../repositories/accounting_repository.dart';
import '../../../core/services/api_service.dart';

class AccountingDocumentProvider extends ChangeNotifier {
  final AccountingRepository _repository = AccountingRepository();
  final ApiService _apiService = ApiService.instance;

  List<AccountingDocument> _accountingDocuments = [];
  AccountingDocument? _selectedAccountingDocument;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _pagination = {};
  Map<String, dynamic> _statistics = {};

  // Getters
  List<AccountingDocument> get accountingDocuments => _accountingDocuments;
  AccountingDocument? get selectedAccountingDocument => _selectedAccountingDocument;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic> get pagination => _pagination;
  Map<String, dynamic> get statistics => _statistics;
  bool get hasError => _error != null;
  bool get isEmpty => _accountingDocuments.isEmpty && !_isLoading;

  // Calculate statistics
  void _calculateStatistics() {
    double totalDebit = 0;
    double totalCredit = 0;
    for (var doc in _accountingDocuments) {
      totalDebit += doc.totalDebit;
      totalCredit += doc.totalCredit;
    }

    final totalDocuments = _accountingDocuments.length;
    final draftDocuments = _accountingDocuments.where((doc) => doc.status == 'draft').length;
    final pendingDocuments = _accountingDocuments.where((doc) => doc.status == 'pending').length;
    final approvedDocuments = _accountingDocuments.where((doc) => doc.status == 'approved').length;
    final rejectedDocuments = _accountingDocuments.where((doc) => doc.status == 'rejected').length;

    _statistics = {
      'total_documents': totalDocuments,
      'draft_documents': draftDocuments,
      'pending_documents': pendingDocuments,
      'approved_documents': approvedDocuments,
      'rejected_documents': rejectedDocuments,
      'total_debit': totalDebit,
      'total_credit': totalCredit,
      'balance_difference': totalDebit - totalCredit,
    };
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  // Load accounting documents
  Future<void> loadAccountingDocuments({
    int page = 1,
    int perPage = 20,
    String? search,
    int? fiscalYearId,
    String? status,
    String? type,
    DateTime? fromDate,
    DateTime? toDate,
    bool refresh = false,
  }) async {
    try {
      if (page == 1 || refresh) {
        _setLoading(true);
      }
      _error = null;

      final result = await _repository.getAccountingDocuments(
        page: page,
        perPage: perPage,
        search: search,
        fiscalYearId: fiscalYearId,
        status: status,
        type: type,
        fromDate: fromDate,
        toDate: toDate,
      );

      final List<AccountingDocument> documents = List<AccountingDocument>.from(result['documents']);
      if (page == 1 || refresh) {
        _accountingDocuments = documents;
      } else {
        _accountingDocuments.addAll(documents);
      }
      
      _pagination = result['pagination'];
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری اسناد حسابداری: ${e.toString()}');
    }
  }

  // Load accounting document by ID
  Future<void> loadAccountingDocument(int id) async {
    try {
      _setLoading(true);
      _error = null;

      _selectedAccountingDocument = await _repository.getAccountingDocument(id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری سند حسابداری: ${e.toString()}');
    }
  }

  // Create accounting document
  Future<bool> createAccountingDocument(Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final newDocument = await _repository.createAccountingDocument(data);
      _accountingDocuments.insert(0, newDocument);
      _selectedAccountingDocument = newDocument;
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ایجاد سند حسابداری: ${e.toString()}');
      return false;
    }
  }

  // Update accounting document
  Future<bool> updateAccountingDocument(int id, Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final updatedDocument = await _repository.updateAccountingDocument(id, data);
      
      final index = _accountingDocuments.indexWhere((doc) => doc.id == id);
      if (index != -1) {
        _accountingDocuments[index] = updatedDocument;
      }
      
      if (_selectedAccountingDocument?.id == id) {
        _selectedAccountingDocument = updatedDocument;
      }
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ویرایش سند حسابداری: ${e.toString()}');
      return false;
    }
  }

  // Delete accounting document
  Future<bool> deleteAccountingDocument(int id) async {
    try {
      _setLoading(true);
      _error = null;

      await _repository.deleteAccountingDocument(id);
      
      _accountingDocuments.removeWhere((doc) => doc.id == id);
      
      if (_selectedAccountingDocument?.id == id) {
        _selectedAccountingDocument = null;
      }
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در حذف سند حسابداری: ${e.toString()}');
      return false;
    }
  }

  // Approve accounting document
  Future<bool> approveAccountingDocument(int id) async {
    try {
      _setLoading(true);
      _error = null;

      final updatedDocument = await _repository.approveAccountingDocument(id);
      
      final index = _accountingDocuments.indexWhere((doc) => doc.id == id);
      if (index != -1) {
        _accountingDocuments[index] = updatedDocument;
      }
      
      if (_selectedAccountingDocument?.id == id) {
        _selectedAccountingDocument = updatedDocument;
      }
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در تأیید سند حسابداری: ${e.toString()}');
      return false;
    }
  }

  // Reject accounting document
  Future<bool> rejectAccountingDocument(int id) async {
    try {
      _setLoading(true);
      _error = null;

      final updatedDocument = await _repository.rejectAccountingDocument(id);
      
      final index = _accountingDocuments.indexWhere((doc) => doc.id == id);
      if (index != -1) {
        _accountingDocuments[index] = updatedDocument;
      }
      
      if (_selectedAccountingDocument?.id == id) {
        _selectedAccountingDocument = updatedDocument;
      }
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در رد سند حسابداری: ${e.toString()}');
      return false;
    }
  }

  // Get documents by status
  List<AccountingDocument> getDocumentsByStatus(String status) {
    return _accountingDocuments.where((doc) => doc.status == status).toList();
  }

  // Get documents by type
  List<AccountingDocument> getDocumentsByType(String type) {
    return _accountingDocuments.where((doc) => doc.type == type).toList();
  }

  // Get documents by fiscal year
  List<AccountingDocument> getDocumentsByFiscalYear(int fiscalYearId) {
    return _accountingDocuments.where((doc) => doc.fiscalYearId == fiscalYearId).toList();
  }

  // Get balanced documents
  List<AccountingDocument> getBalancedDocuments() {
    return _accountingDocuments.where((doc) => doc.totalDebit == doc.totalCredit).toList();
  }

  // Get unbalanced documents
  List<AccountingDocument> getUnbalancedDocuments() {
    return _accountingDocuments.where((doc) => doc.totalDebit != doc.totalCredit).toList();
  }

  // Clear data
  void clearData() {
    _accountingDocuments.clear();
    _selectedAccountingDocument = null;
    _pagination.clear();
    _statistics.clear();
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  // Generate next document number
  String generateNextDocumentNumber() {
    if (_accountingDocuments.isEmpty) return '1';
    
    final numbers = _accountingDocuments
        .map((doc) => int.tryParse(doc.documentNumber) ?? 0)
        .where((number) => number > 0)
        .toList();
    
    if (numbers.isEmpty) return '1';
    
    numbers.sort();
    return (numbers.last + 1).toString();
  }
}
