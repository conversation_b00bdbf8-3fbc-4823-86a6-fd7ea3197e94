class AppConfig {
  // API Configuration
  static const String baseUrl = 'http://localhost:8001/api';
  static const String apiVersion = '';
  static const String apiBaseUrl = baseUrl;

  // Environment Configuration
  static const bool isDevelopment = true;
  
  // API Endpoints
  // Prefix for mobile-app API (v1)
  static const String loginEndpoint = '/v1/auth/login';
  static const String logoutEndpoint = '/v1/auth/logout';
  static const String userInfoEndpoint = '/v1/user';
  static const String changePasswordEndpoint = '/v1/auth/change-password';
  static const String updateProfileEndpoint = '/v1/user/profile';
  static const String machineriesEndpoint = '/v1/machineries';
  static const String machineryCategoriesEndpoint = '/v1/machineries/categories';
  static const String machineryStatsEndpoint = '/v1/machineries/statistics';
  static const String machinerySearchEndpoint = '/v1/machineries/search';
  static const String exhibitionsEndpoint = '/v1/exhibitions';
  static const String machineryEventsEndpoint = '/v1/machinery-events';
  
  // CRM Module Endpoints
  static const String contactsEndpoint = '/v1/crm/contacts';
  static const String leadsEndpoint = '/v1/crm/leads';
  static const String opportunitiesEndpoint = '/v1/crm/opportunities';
  static const String pipelinesEndpoint = '/v1/crm/pipelines';
  static const String invoicesEndpoint = '/v1/crm/invoices';
  static const String productsEndpoint = '/v1/crm/products';
  static const String activitiesEndpoint = '/v1/crm/activities';
  
  // Accounting Module Endpoints
  static const String accountStructuresEndpoint = '/v1/accounting/account-structures';
  static const String chartOfAccountsEndpoint = '/v1/accounting/chart-of-accounts';
  static const String fiscalYearsEndpoint = '/v1/accounting/fiscal-years';
  static const String accountingDocumentsEndpoint = '/v1/accounting/documents';
  static const String banksEndpoint = '/v1/accounting/banks';
  static const String bankAccountsEndpoint = '/v1/accounting/bank-accounts';
  static const String cashRegistersEndpoint = '/v1/accounting/cash-registers';
  static const String checkbooksEndpoint = '/v1/accounting/checkbooks';
  static const String receivedChecksEndpoint = '/v1/accounting/received-checks';
  static const String issuedChecksEndpoint = '/v1/accounting/issued-checks';
  static const String fundTransfersEndpoint = '/v1/accounting/fund-transfers';
  static const String financialTransactionsEndpoint = '/v1/accounting/financial-transactions';
  
  // Task Module Endpoints
  static const String tasksEndpoint = '/v1/tasks';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String userKey = 'user_data';
  static const String languageKey = 'app_language';
  static const String themeKey = 'app_theme';
  
  // App Configuration
  static const String appName = 'سنگین فروش';
  static const String appVersion = '1.0.0';
  static const int requestTimeout = 30; // seconds
  static const int maxRetries = 3;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const Duration shortCacheExpiration = Duration(minutes: 30);
}