import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../models/ai_agent.dart';
import '../providers/ai_provider.dart';
import '../widgets/agent_card.dart';
// import '../widgets/agent_search_filter.dart'; // Temporarily commented out

class AIAgentsScreen extends StatefulWidget {
  final String agentId;
  
  const AIAgentsScreen({super.key, required this.agentId});

  @override
  State<AIAgentsScreen> createState() => _AIAgentsScreenState();
}

class _AIAgentsScreenState extends State<AIAgentsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'name';
  bool _showActiveOnly = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AIProvider>().loadAgents();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<AIAgent> _filterAndSortAgents(List<AIAgent> agents) {
    var filteredAgents = agents.where((agent) {
      final matchesSearch = _searchQuery.isEmpty ||
          agent.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          agent.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          agent.capabilities.any((cap) => cap.toLowerCase().contains(_searchQuery.toLowerCase()));
      
      final matchesStatus = !_showActiveOnly || agent.isActive;
      
      return matchesSearch && matchesStatus;
    }).toList();

    // Sort agents
    switch (_sortBy) {
      case 'name':
        filteredAgents.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'conversations':
        filteredAgents.sort((a, b) => b.conversationCount.compareTo(a.conversationCount));
        break;
      case 'rating':
        filteredAgents.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'lastUsed':
        filteredAgents.sort((a, b) {
          if (a.lastUsed == null && b.lastUsed == null) return 0;
          if (a.lastUsed == null) return 1;
          if (b.lastUsed == null) return -1;
          return b.lastUsed!.compareTo(a.lastUsed!);
        });
        break;
    }

    return filteredAgents;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'دستیاران هوشمند',
          style: TextStyle(
            fontFamily: 'IranSansX',
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics_outlined),
            onPressed: _showStatisticsDialog,
            tooltip: 'آمار کلی',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<AIProvider>().loadAgents();
            },
            tooltip: 'بروزرسانی',
          ),
        ],
      ),
      body: Consumer<AIProvider>(
        builder: (context, aiProvider, child) {
          final isLoading = aiProvider.isAgentsLoading;
          final error = aiProvider.agentsError;
          final agents = aiProvider.agents;

          if (isLoading && agents.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (error != null && agents.isEmpty) {
            return _buildErrorState(error, () {
              aiProvider.loadAgents();
            });
          }

          final filteredAgents = _filterAndSortAgents(agents);

          return Column(
            children: [
              // Search and Filter Section
              Container(
                padding: const EdgeInsets.all(16),
                color: Theme.of(context).cardColor,
                child: Column(
                  children: [
                    // Search Field
                    CustomTextField(
                      controller: _searchController,
                      hintText: 'جستجو در دستیاران...',
                      prefixIcon: const Icon(Icons.search),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                          : null,
                    ),
                    const SizedBox(height: 12),
                    
                    // Filter Row
                    Row(
                      children: [
                        // Sort Dropdown
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _sortBy,
                            decoration: const InputDecoration(
                              labelText: 'مرتب‌سازی بر اساس',
                              labelStyle: TextStyle(fontFamily: 'IranSansX'),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            items: const [
                              DropdownMenuItem(
                                value: 'name',
                                child: Text('نام', style: TextStyle(fontFamily: 'IranSansX')),
                              ),
                              DropdownMenuItem(
                                value: 'conversations',
                                child: Text('تعداد مکالمه', style: TextStyle(fontFamily: 'IranSansX')),
                              ),
                              DropdownMenuItem(
                                value: 'rating',
                                child: Text('امتیاز', style: TextStyle(fontFamily: 'IranSansX')),
                              ),
                              DropdownMenuItem(
                                value: 'lastUsed',
                                child: Text('آخرین استفاده', style: TextStyle(fontFamily: 'IranSansX')),
                              ),
                            ],
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _sortBy = value;
                                });
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        
                        // Active Only Filter
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text(
                              'فقط فعال‌ها',
                              style: TextStyle(fontFamily: 'IranSansX'),
                            ),
                            value: _showActiveOnly,
                            onChanged: (value) {
                              setState(() {
                                _showActiveOnly = value ?? false;
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Results Count
              if (agents.isNotEmpty)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  color: Colors.grey.shade100,
                  child: Text(
                    '${filteredAgents.length} دستیار از ${agents.length} دستیار',
                    style: const TextStyle(
                      fontFamily: 'IranSansX',
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ),

              // Agents List
              Expanded(
                child: filteredAgents.isEmpty
                    ? _buildEmptyState()
                    : RefreshIndicator(
                        onRefresh: () async {
                          if (!mounted) return;
                          await context.read<AIProvider>().loadAgents();
                        },
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: filteredAgents.length,
                          itemBuilder: (context, index) {
                            final agent = filteredAgents[index];
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: AgentCard(
                                agent: agent,
                                onTap: () {
                                  context.push('/ai/agents/${agent.id}/chat');
                                },
                              ),
                            );
                          },
                        ),
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.smart_toy_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty ? 'نتیجه‌ای یافت نشد' : 'دستیاری موجود نیست',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
              fontFamily: 'IranSansX',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? 'لطفاً کلمات کلیدی دیگری امتحان کنید'
                : 'هیچ دستیار هوشمندی تعریف نشده است',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
              fontFamily: 'IranSansX',
            ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isNotEmpty) ..[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _searchController.clear();
                setState(() {
                  _searchQuery = '';
                });
              },
              child: const Text(
                'پاک کردن جستجو',
                style: TextStyle(fontFamily: 'IranSansX'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorState(String error, VoidCallback onRetry) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'خطا در بارگذاری',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red.shade600,
              fontFamily: 'IranSansX',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
              fontFamily: 'IranSansX',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRetry,
            child: const Text(
              'تلاش مجدد',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
          ),
        ],
      ),
    );
  }

  void _showStatisticsDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'آمار کلی دستیاران',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'IranSansX',
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const Divider(),
              Consumer<AIProvider>(
                builder: (context, aiProvider, child) {
                  final agents = aiProvider.agents;
                  final activeAgents = agents.where((a) => a.isActive).length;
                  final totalConversations = agents.fold<int>(0, (sum, agent) => sum + agent.conversationCount);
                  final averageRating = agents.isNotEmpty
                      ? agents.fold<double>(0, (sum, agent) => sum + agent.rating) / agents.length
                      : 0.0;

                  return Column(
                    children: [
                      _buildStatCard('کل دستیاران', agents.length.toString(), Icons.smart_toy),
                      const SizedBox(height: 12),
                      _buildStatCard('دستیاران فعال', activeAgents.toString(), Icons.check_circle),
                      const SizedBox(height: 12),
                      _buildStatCard('کل مکالمات', totalConversations.toString(), Icons.chat),
                      const SizedBox(height: 12),
                      _buildStatCard('میانگین امتیاز', averageRating.toStringAsFixed(1), Icons.star),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 32,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                    fontFamily: 'IranSansX',
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'IranSansX',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}