import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/security_provider.dart';
import '../widgets/permission_gate.dart';
import 'security_center_screen.dart';
import 'roles_management_screen.dart';
import 'permissions_management_screen.dart';

class SecurityDashboardScreen extends StatefulWidget {
  static const String routeName = '/security-dashboard';
  
  const SecurityDashboardScreen({super.key});

  @override
  State<SecurityDashboardScreen> createState() => _SecurityDashboardScreenState();
}

class _SecurityDashboardScreenState extends State<SecurityDashboardScreen> {
  bool _isInit = false;
  bool _isLoading = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInit) {
      _loadData();
      _isInit = true;
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<SecurityProvider>(context, listen: false).loadInitialData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطا در بارگذاری اطلاعات: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مرکز امنیت'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'بارگذاری مجدد',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Consumer<SecurityProvider>(
              builder: (ctx, securityProvider, _) {
                if (securityProvider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'خطا در بارگذاری اطلاعات: ${securityProvider.error}',
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadData,
                          child: const Text('تلاش مجدد'),
                        ),
                      ],
                    ),
                  );
                }

                return _buildDashboardContent();
              },
            ),
    );
  }

  Widget _buildDashboardContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'مرکز امنیت و دسترسی‌ها',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'در این بخش می‌توانید دسترسی‌ها و نقش‌های خود را مشاهده کنید و در صورت داشتن مجوز، آن‌ها را مدیریت نمایید.',
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 24),
          _buildSecurityCard(
            title: 'مرکز امنیت من',
            description: 'مشاهده نقش‌ها و دسترسی‌های کاربری شما',
            icon: Icons.security,
            onTap: () {
              Navigator.pushNamed(context, SecurityCenterScreen.routeName);
            },
          ),
          const SizedBox(height: 16),
          PermissionGate(
            permissionName: 'roles.view',
            child: _buildSecurityCard(
              title: 'مدیریت نقش‌ها',
              description: 'مشاهده و مدیریت نقش‌های کاربری در سیستم',
              icon: Icons.admin_panel_settings,
              onTap: () {
                Navigator.pushNamed(context, RolesManagementScreen.routeName);
              },
            ),
            fallback: const SizedBox.shrink(),
          ),
          const SizedBox(height: 16),
          PermissionGate(
            permissionName: 'permissions.view',
            child: _buildSecurityCard(
              title: 'مدیریت دسترسی‌ها',
              description: 'مشاهده و مدیریت دسترسی‌های سیستم',
              icon: Icons.vpn_key,
              onTap: () {
                Navigator.pushNamed(context, PermissionsManagementScreen.routeName);
              },
            ),
            fallback: const SizedBox.shrink(),
          ),
          const SizedBox(height: 16),
          PermissionGate(
            permissionName: 'users.view',
            child: _buildSecurityCard(
              title: 'مدیریت کاربران',
              description: 'مشاهده و مدیریت کاربران و دسترسی‌های آن‌ها',
              icon: Icons.people,
              onTap: () {
                // در اینجا می‌توان به صفحه مدیریت کاربران هدایت کرد
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('این قابلیت هنوز پیاده‌سازی نشده است')),
                );
              },
            ),
            fallback: const SizedBox.shrink(),
          ),
          const SizedBox(height: 16),
          PermissionGate(
            permissionName: 'security.logs',
            child: _buildSecurityCard(
              title: 'گزارش‌های امنیتی',
              description: 'مشاهده لاگ‌ها و گزارش‌های امنیتی سیستم',
              icon: Icons.assessment,
              onTap: () {
                // در اینجا می‌توان به صفحه گزارش‌های امنیتی هدایت کرد
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('این قابلیت هنوز پیاده‌سازی نشده است')),
                );
              },
            ),
            fallback: const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityCard({
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}