import 'package:flutter/material.dart';
import '../models/models.dart';
import '../repositories/accounting_repository.dart';

class CashRegisterProvider extends ChangeNotifier {
  final AccountingRepository _repository;

  CashRegisterProvider() : _repository = AccountingRepository();

  List<CashRegister> _cashRegisters = [];
  CashRegister? _selectedCashRegister;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _pagination = {};
  Map<String, dynamic> _statistics = {};

  // Getters
  List<CashRegister> get cashRegisters => _cashRegisters;
  CashRegister? get selectedCashRegister => _selectedCashRegister;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic> get pagination => _pagination;
  Map<String, dynamic> get statistics => _statistics;
  bool get hasError => _error != null;
  bool get isEmpty => _cashRegisters.isEmpty && !_isLoading;

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error state
  void _setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  // Load cash registers
  Future<void> loadCashRegisters({
    int page = 1,
    int perPage = 20,
    String? search,
    int? chartOfAccountId,
    int? cashierId,
    bool? isActive,
    bool refresh = false,
  }) async {
    try {
      if (refresh || page == 1) {
        _setLoading(true);
        _error = null;
      }

      final result = await _repository.getCashRegisters(
        page: page,
        perPage: perPage,
        search: search,
        chartOfAccountId: chartOfAccountId,
        cashierId: cashierId,
        isActive: isActive,
      );

      if (page == 1 || refresh) {
        _cashRegisters = result['registers'];
      } else {
        _cashRegisters.addAll(result['registers']);
      }
      
      _pagination = result['pagination'];
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری صندوق‌های نقدی: ${e.toString()}');
    }
  }

  // Load cash register by ID
  Future<void> loadCashRegister(int id) async {
    try {
      _setLoading(true);
      _error = null;

      _selectedCashRegister = await _repository.getCashRegister(id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('خطا در بارگذاری صندوق نقدی: ${e.toString()}');
    }
  }

  // Create cash register
  Future<bool> createCashRegister(Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final newRegister = await _repository.createCashRegister(data);
      _cashRegisters.insert(0, newRegister);
      _selectedCashRegister = newRegister;
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ایجاد صندوق نقدی: ${e.toString()}');
      return false;
    }
  }

  // Update cash register
  Future<bool> updateCashRegister(int id, Map<String, dynamic> data) async {
    try {
      _setLoading(true);
      _error = null;

      final updatedRegister = await _repository.updateCashRegister(id, data);
      
      final index = _cashRegisters.indexWhere((reg) => reg.id == id);
      if (index != -1) {
        _cashRegisters[index] = updatedRegister;
      }
      
      if (_selectedCashRegister?.id == id) {
        _selectedCashRegister = updatedRegister;
      }
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در ویرایش صندوق نقدی: ${e.toString()}');
      return false;
    }
  }

  // Delete cash register
  Future<bool> deleteCashRegister(int id) async {
    try {
      _setLoading(true);
      _error = null;

      await _repository.deleteCashRegister(id);
      
      _cashRegisters.removeWhere((reg) => reg.id == id);
      
      if (_selectedCashRegister?.id == id) {
        _selectedCashRegister = null;
      }
      
      _calculateStatistics();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطا در حذف صندوق نقدی: ${e.toString()}');
      return false;
    }
  }

  // Activate/Deactivate cash register
  Future<bool> toggleCashRegisterStatus(int id) async {
    try {
      final register = getCashRegisterById(id);
      if (register == null) return false;

      return await updateCashRegister(id, {'is_active': !register.isActive});
    } catch (e) {
      _setError('خطا در تغییر وضعیت صندوق نقدی: ${e.toString()}');
      return false;
    }
  }

  // Update cash register balance
  Future<bool> updateCashRegisterBalance(int id, double newBalance) async {
    try {
      return await updateCashRegister(id, {'balance': newBalance});
    } catch (e) {
      _setError('خطا در به‌روزرسانی موجودی صندوق: ${e.toString()}');
      return false;
    }
  }

  // Calculate statistics
  void _calculateStatistics() {
    if (_cashRegisters.isEmpty) {
      _statistics = {};
      return;
    }

    final totalRegisters = _cashRegisters.length;
    final activeRegisters = _cashRegisters.where((reg) => reg.isActive).length;
    final inactiveRegisters = totalRegisters - activeRegisters;
    
    double totalBalance = 0;
    double activeBalance = 0;
    double positiveBalance = 0;
    double negativeBalance = 0;
    
    for (final register in _cashRegisters) {
      totalBalance += register.balance;
      if (register.isActive) {
        activeBalance += register.balance;
      }
      if (register.balance > 0) {
        positiveBalance += register.balance;
      } else if (register.balance < 0) {
        negativeBalance += register.balance;
      }
    }

    _statistics = {
      'total_registers': totalRegisters,
      'active_registers': activeRegisters,
      'inactive_registers': inactiveRegisters,
      'total_balance': totalBalance,
      'active_balance': activeBalance,
      'positive_balance': positiveBalance,
      'negative_balance': negativeBalance,
    };
  }

  // Get registers by cashier
  List<CashRegister> getRegistersByCashier(int cashierId) {
    return _cashRegisters.where((reg) => reg.cashierId == cashierId).toList();
  }

  // Get active registers
  List<CashRegister> getActiveRegisters() {
    return _cashRegisters.where((reg) => reg.isActive).toList();
  }

  // Get inactive registers
  List<CashRegister> getInactiveRegisters() {
    return _cashRegisters.where((reg) => !reg.isActive).toList();
  }

  // Get registers with positive balance
  List<CashRegister> getRegistersWithPositiveBalance() {
    return _cashRegisters.where((reg) => reg.balance > 0).toList();
  }

  // Get registers with negative balance
  List<CashRegister> getRegistersWithNegativeBalance() {
    return _cashRegisters.where((reg) => reg.balance < 0).toList();
  }

  // Get registers with zero balance
  List<CashRegister> getRegistersWithZeroBalance() {
    return _cashRegisters.where((reg) => reg.balance == 0).toList();
  }

  // Select cash register
  void selectCashRegister(CashRegister? register) {
    _selectedCashRegister = register;
    notifyListeners();
  }

  // Clear selected cash register
  void clearSelection() {
    _selectedCashRegister = null;
    notifyListeners();
  }

  // Search cash registers
  Future<void> searchCashRegisters(String query) async {
    await loadCashRegisters(search: query, refresh: true);
  }

  // Filter by chart of account
  Future<void> filterByChartOfAccount(int chartOfAccountId) async {
    await loadCashRegisters(chartOfAccountId: chartOfAccountId, refresh: true);
  }

  // Filter by cashier
  Future<void> filterByCashier(int cashierId) async {
    await loadCashRegisters(cashierId: cashierId, refresh: true);
  }

  // Filter by active status
  Future<void> filterByActiveStatus(bool isActive) async {
    await loadCashRegisters(isActive: isActive, refresh: true);
  }

  // Refresh cash registers
  Future<void> refresh() async {
    await loadCashRegisters(refresh: true);
  }

  // Load more cash registers (pagination)
  Future<void> loadMore() async {
    if (_isLoading) return;
    
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    
    if (currentPage < lastPage) {
      await loadCashRegisters(page: currentPage + 1);
    }
  }

  // Check if can load more
  bool get canLoadMore {
    final currentPage = _pagination['current_page'] ?? 1;
    final lastPage = _pagination['last_page'] ?? 1;
    return currentPage < lastPage && !_isLoading;
  }

  // Get cash register by ID from loaded list
  CashRegister? getCashRegisterById(int id) {
    try {
      return _cashRegisters.firstWhere((reg) => reg.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get cash register by name
  CashRegister? getCashRegisterByName(String name) {
    try {
      return _cashRegisters.firstWhere((reg) => reg.name.toLowerCase() == name.toLowerCase());
    } catch (e) {
      return null;
    }
  }

  // Get cash registers for dropdown
  List<Map<String, dynamic>> getCashRegistersForDropdown({bool activeOnly = true}) {
    final registers = activeOnly ? getActiveRegisters() : _cashRegisters;
    return registers.map((register) => {
      'value': register.id,
      'label': register.name,
      'accountName': register.accountName,
      'cashierName': register.cashierName,
      'balance': register.balance,
      'formattedBalance': register.formattedBalance,
      'isActive': register.isActive,
    }).toList();
  }


}