import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../constants/app_config.dart';
import '../errors/api_exception.dart';
import '../middleware/exhibition_middleware.dart';
import '../../features/exhibition/models/exhibition.dart';

/// سرویس اصلی برای ارتباط با API
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  late final Dio _dio;
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _tokenKey = 'auth_token';

  /// مقداردهی اولیه سرویس
  void initialize() {
    _dio = Dio();
    
    // تنظیمات پایه
    _dio.options.baseUrl = AppConfig.baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);
    
    // اضافه کردن headers پیش‌فرض
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // اضافه کردن interceptor برای لاگ
    if (AppConfig.isDevelopment) {
      _dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
      ));
    }

    // اضافه کردن interceptor برای توکن
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          await clearToken();
        }
        handler.next(error);
      },
    ));

    // اضافه کردن middleware نمایشگاه
    _dio.interceptors.add(ExhibitionMiddleware());
  }

  /// دریافت instance از Dio
  Dio get dio => _dio;

  /// ذخیره توکن در حافظه امن
  static Future<void> saveToken(String token) async {
    await _storage.write(key: _tokenKey, value: token);
  }

  /// دریافت توکن از حافظه امن
  static Future<String?> getToken() async {
    return await _storage.read(key: _tokenKey);
  }

  /// پاک کردن توکن از حافظه امن
  static Future<void> clearToken() async {
    await _storage.delete(key: _tokenKey);
  }

  /// حذف توکن از حافظه امن (alias برای clearToken)
  static Future<void> removeToken() async {
    await clearToken();
  }

  /// بررسی وجود توکن
  static Future<bool> hasToken() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  /// مدیریت خطاهای Dio و تبدیل آن به ApiException
  ApiException _handleError(DioException e) {
    String errorMessage = 'یک خطای ناشناخته رخ داد.';

    if (e.response != null) {
      final responseData = e.response!.data;
      if (responseData is Map && responseData.containsKey('message')) {
        errorMessage = responseData['message'];
      } else {
        errorMessage = 'پاسخ نامعتبر از سرور دریافت شد.';
      }
    } else if (e.type == DioExceptionType.connectionTimeout ||
               e.type == DioExceptionType.receiveTimeout ||
               e.type == DioExceptionType.sendTimeout) {
      errorMessage = 'زمان ارتباط با سرور به پایان رسید.';
    } else if (e.type == DioExceptionType.unknown) {
      errorMessage = 'خطا در ارتباط با سرور.';
    }

    return ApiException(
      message: errorMessage,
      displayMessage: errorMessage,
      statusCode: e.response?.statusCode,
      errors: e.response?.data is Map ? e.response?.data['errors'] : null,
    );
  }

  /// متد عمومی برای مدیریت خطاها
  ApiException handleError(dynamic error) {
    if (error is DioException) {
      return _handleError(error);
    } else if (error is ApiException) {
      return error;
    } else {
      return ApiException(
        message: error.toString(),
        displayMessage: 'خطای غیرمنتظره‌ای رخ داد.',
        statusCode: null,
      );
    }
  }

  /// دریافت instance از ApiService
  static ApiService get instance => _instance;

  /// ورود کاربر
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await _dio.post(
        AppConfig.loginEndpoint,
        data: {'email': email, 'password': password},
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'عملیات موفق نبود',
          displayMessage: response.data['message'] ?? 'عملیات موفق نبود',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت لیست ماشین‌آلات
  Future<Map<String, dynamic>> getMachineries({
    int page = 1,
    int perPage = 20,
    String? search,
    String? category,
    String? status,
    String? location,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (category != null) queryParams['category'] = category;
      if (status != null) queryParams['status'] = status;
      if (location != null) queryParams['location'] = location;

      final response = await _dio.get('/api/machineries', queryParameters: queryParams);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت لیست ماشین‌آلات',
          displayMessage: response.data['message'] ?? 'خطا در دریافت لیست ماشین‌آلات',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت اطلاعات یک ماشین‌آلات
  Future<Map<String, dynamic>> getMachinery(int id) async {
    try {
      final response = await _dio.get('/api/machineries/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت اطلاعات ماشین‌آلات',
          displayMessage: response.data['message'] ?? 'خطا در دریافت اطلاعات ماشین‌آلات',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// ایجاد ماشین‌آلات جدید
  Future<Map<String, dynamic>> createMachinery(Map<String, dynamic> data) async {
    try {
      final response = await _dio.post('/api/machineries', data: data);
      
      if (response.statusCode == 201 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در ایجاد ماشین‌آلات',
          displayMessage: response.data['message'] ?? 'خطا در ایجاد ماشین‌آلات',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// به‌روزرسانی ماشین‌آلات
  Future<Map<String, dynamic>> updateMachinery(int id, Map<String, dynamic> data) async {
    try {
      final response = await _dio.put('/api/machineries/$id', data: data);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در به‌روزرسانی ماشین‌آلات',
          displayMessage: response.data['message'] ?? 'خطا در به‌روزرسانی ماشین‌آلات',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// حذف ماشین‌آلات
  Future<Map<String, dynamic>> deleteMachinery(int id) async {
    try {
      final response = await _dio.delete('/api/machineries/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در حذف ماشین‌آلات',
          displayMessage: response.data['message'] ?? 'خطا در حذف ماشین‌آلات',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت آمار ماشین‌آلات
  Future<Map<String, dynamic>> getMachineryStatistics() async {
    try {
      final response = await _dio.get('/api/machineries/statistics');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت آمار ماشین‌آلات',
          displayMessage: response.data['message'] ?? 'خطا در دریافت آمار ماشین‌آلات',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت دسته‌بندی‌های ماشین‌آلات
  Future<Map<String, dynamic>> getMachineryCategories() async {
    try {
      final response = await _dio.get('/api/machinery-categories');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت دسته‌بندی‌ها',
          displayMessage: response.data['message'] ?? 'خطا در دریافت دسته‌بندی‌ها',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت سازندگان ماشین‌آلات
  Future<Map<String, dynamic>> getMachineryManufacturers() async {
    try {
      final response = await _dio.get('/api/machinery-manufacturers');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت سازندگان',
          displayMessage: response.data['message'] ?? 'خطا در دریافت سازندگان',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت مکان‌های ماشین‌آلات
  Future<Map<String, dynamic>> getMachineryLocations() async {
    try {
      final response = await _dio.get('/api/machinery-locations');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت مکان‌ها',
          displayMessage: response.data['message'] ?? 'خطا در دریافت مکان‌ها',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت مدل‌های ماشین‌آلات
  Future<Map<String, dynamic>> getMachineryModels() async {
    try {
      final response = await _dio.get('/api/machinery-models');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت مدل‌ها',
          displayMessage: response.data['message'] ?? 'خطا در دریافت مدل‌ها',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت لیست رویدادهای ماشین‌آلات
  Future<Map<String, dynamic>> getMachineryEvents({
    int page = 1,
    int perPage = 20,
    String? search,
    int? machineryId,
    String? eventType,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (machineryId != null) queryParams['machinery_id'] = machineryId;
      if (eventType != null) queryParams['event_type'] = eventType;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T').first;
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T').first;

      final response = await _dio.get('/api/machinery-events', queryParameters: queryParams);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت رویدادهای ماشین‌آلات',
          displayMessage: response.data['message'] ?? 'خطا در دریافت رویدادهای ماشین‌آلات',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// ایجاد رویداد جدید برای ماشین‌آلات
  Future<Map<String, dynamic>> createMachineryEvent(Map<String, dynamic> data) async {
    try {
      final response = await _dio.post('/api/machinery-events', data: data);
      
      if (response.statusCode == 201 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در ایجاد رویداد',
          displayMessage: response.data['message'] ?? 'خطا در ایجاد رویداد',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// به‌روزرسانی رویداد ماشین‌آلات
  Future<Map<String, dynamic>> updateMachineryEvent(int id, Map<String, dynamic> data) async {
    try {
      final response = await _dio.put('/api/machinery-events/$id', data: data);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در به‌روزرسانی رویداد',
          displayMessage: response.data['message'] ?? 'خطا در به‌روزرسانی رویداد',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// حذف رویداد ماشین‌آلات
  Future<Map<String, dynamic>> deleteMachineryEvent(int id) async {
    try {
      final response = await _dio.delete('/api/machinery-events/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در حذف رویداد',
          displayMessage: response.data['message'] ?? 'خطا در حذف رویداد',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت پروفایل کاربر
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final response = await _dio.get('/api/user/profile');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت پروفایل کاربر',
          displayMessage: response.data['message'] ?? 'خطا در دریافت پروفایل کاربر',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// به‌روزرسانی پروفایل کاربر
  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> data) async {
    try {
      final response = await _dio.put('/api/user/profile', data: data);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در به‌روزرسانی پروفایل',
          displayMessage: response.data['message'] ?? 'خطا در به‌روزرسانی پروفایل',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت لیست نمایشگاه‌های موجود
  Future<List<Exhibition>> getAvailableExhibitions() async {
    try {
      final response = await _dio.get('/api/v1/exhibitions');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> exhibitionsData = response.data['data'];
        return exhibitionsData.map((json) => Exhibition.fromJson(json)).toList();
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت نمایشگاه‌ها',
          displayMessage: response.data['message'] ?? 'خطا در دریافت نمایشگاه‌ها',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت اطلاعات یک نمایشگاه خاص
  Future<Exhibition> getExhibition(int exhibitionId) async {
    try {
      final response = await _dio.get('/api/v1/exhibitions/$exhibitionId');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return Exhibition.fromJson(response.data['data']);
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت اطلاعات نمایشگاه',
          displayMessage: response.data['message'] ?? 'خطا در دریافت اطلاعات نمایشگاه',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت نمایشگاه‌های کاربر
  Future<List<Exhibition>> getUserExhibitions() async {
    try {
      final response = await _dio.get('/api/v1/user/exhibitions');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> exhibitionsData = response.data['data'];
        return exhibitionsData.map((json) => Exhibition.fromJson(json)).toList();
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت نمایشگاه‌های کاربر',
          displayMessage: response.data['message'] ?? 'خطا در دریافت نمایشگاه‌های کاربر',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// تنظیم نمایشگاه فعال
  Future<Map<String, dynamic>> setActiveExhibition(int exhibitionId) async {
    try {
      final response = await _dio.post(
        '/api/v1/user/active-exhibition',
        data: {'exhibition_id': exhibitionId},
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در تنظیم نمایشگاه فعال',
          displayMessage: response.data['message'] ?? 'خطا در تنظیم نمایشگاه فعال',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت نمایشگاه فعال کاربر
  Future<Exhibition?> getActiveExhibition() async {
    try {
      final response = await _dio.get('/api/v1/user/active-exhibition');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final exhibitionData = response.data['data'];
        if (exhibitionData != null) {
          return Exhibition.fromJson(exhibitionData);
        }
        return null;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت نمایشگاه فعال',
          displayMessage: response.data['message'] ?? 'خطا در دریافت نمایشگاه فعال',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // ==================== CRM API Methods ====================

  /// دریافت لیست فاکتورها
  Future<Map<String, dynamic>> getInvoices({
    int page = 1,
    int limit = 20,
    String? search,
    String? status,
    int? contactId,
    int? opportunityId,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (search != null) queryParams['search'] = search;
      if (status != null) queryParams['status'] = status;
      if (contactId != null) queryParams['contact_id'] = contactId;
      if (opportunityId != null) queryParams['opportunity_id'] = opportunityId;
      if (sortBy != null) queryParams['sort_by'] = sortBy;
      if (sortOrder != null) queryParams['sort_order'] = sortOrder;

      final response = await _dio.get('/api/crm/invoices', queryParameters: queryParams);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت فاکتورها',
          displayMessage: response.data['message'] ?? 'خطا در دریافت فاکتورها',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت اطلاعات یک فاکتور
  Future<Map<String, dynamic>> getInvoice(dynamic id) async {
    try {
      final response = await _dio.get('/api/crm/invoices/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data['data'];
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت فاکتور',
          displayMessage: response.data['message'] ?? 'خطا در دریافت فاکتور',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// ایجاد فاکتور جدید
  Future<Map<String, dynamic>> createInvoice(Map<String, dynamic> data) async {
    try {
      final response = await _dio.post('/api/crm/invoices', data: data);
      
      if (response.statusCode == 201 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در ایجاد فاکتور',
          displayMessage: response.data['message'] ?? 'خطا در ایجاد فاکتور',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// به‌روزرسانی فاکتور
  Future<Map<String, dynamic>> updateInvoice(dynamic id, Map<String, dynamic> data) async {
    try {
      final response = await _dio.put('/api/crm/invoices/$id', data: data);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در به‌روزرسانی فاکتور',
          displayMessage: response.data['message'] ?? 'خطا در به‌روزرسانی فاکتور',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// حذف فاکتور
  Future<Map<String, dynamic>> deleteInvoice(dynamic id) async {
    try {
      final response = await _dio.delete('/api/crm/invoices/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در حذف فاکتور',
          displayMessage: response.data['message'] ?? 'خطا در حذف فاکتور',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت لیست محصولات
  Future<Map<String, dynamic>> getProducts({
    int page = 1,
    int limit = 20,
    String? search,
    bool? active,
    int? categoryId,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (search != null) queryParams['search'] = search;
      if (active != null) queryParams['active'] = active;
      if (categoryId != null) queryParams['category_id'] = categoryId;
      if (sortBy != null) queryParams['sort_by'] = sortBy;
      if (sortOrder != null) queryParams['sort_order'] = sortOrder;

      final response = await _dio.get('/api/crm/products', queryParameters: queryParams);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت محصولات',
          displayMessage: response.data['message'] ?? 'خطا در دریافت محصولات',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت اطلاعات یک محصول
  Future<Map<String, dynamic>> getProduct(dynamic id) async {
    try {
      final response = await _dio.get('/api/crm/products/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data['data'];
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت محصول',
          displayMessage: response.data['message'] ?? 'خطا در دریافت محصول',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// ایجاد محصول جدید
  Future<Map<String, dynamic>> createProduct(Map<String, dynamic> data) async {
    try {
      final response = await _dio.post('/api/crm/products', data: data);
      
      if (response.statusCode == 201 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در ایجاد محصول',
          displayMessage: response.data['message'] ?? 'خطا در ایجاد محصول',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// به‌روزرسانی محصول
  Future<Map<String, dynamic>> updateProduct(dynamic id, Map<String, dynamic> data) async {
    try {
      final response = await _dio.put('/api/crm/products/$id', data: data);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در به‌روزرسانی محصول',
          displayMessage: response.data['message'] ?? 'خطا در به‌روزرسانی محصول',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// حذف محصول
  Future<Map<String, dynamic>> deleteProduct(dynamic id) async {
    try {
      final response = await _dio.delete('/api/crm/products/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در حذف محصول',
          displayMessage: response.data['message'] ?? 'خطا در حذف محصول',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت لیست فعالیت‌ها
  Future<Map<String, dynamic>> getActivities({
    int page = 1,
    int limit = 20,
    String? search,
    String? type,
    String? status,
    int? contactId,
    int? opportunityId,
    int? leadId,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (search != null) queryParams['search'] = search;
      if (type != null) queryParams['type'] = type;
      if (status != null) queryParams['status'] = status;
      if (contactId != null) queryParams['contact_id'] = contactId;
      if (opportunityId != null) queryParams['opportunity_id'] = opportunityId;
      if (leadId != null) queryParams['lead_id'] = leadId;
      if (sortBy != null) queryParams['sort_by'] = sortBy;
      if (sortOrder != null) queryParams['sort_order'] = sortOrder;

      final response = await _dio.get('/api/crm/activities', queryParameters: queryParams);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت فعالیت‌ها',
          displayMessage: response.data['message'] ?? 'خطا در دریافت فعالیت‌ها',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت اطلاعات یک فعالیت
  Future<Map<String, dynamic>> getActivity(dynamic id) async {
    try {
      final response = await _dio.get('/api/crm/activities/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data['data'];
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت فعالیت',
          displayMessage: response.data['message'] ?? 'خطا در دریافت فعالیت',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// ایجاد فعالیت جدید
  Future<Map<String, dynamic>> createActivity(Map<String, dynamic> data) async {
    try {
      final response = await _dio.post('/api/crm/activities', data: data);
      
      if (response.statusCode == 201 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در ایجاد فعالیت',
          displayMessage: response.data['message'] ?? 'خطا در ایجاد فعالیت',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// به‌روزرسانی فعالیت
  Future<Map<String, dynamic>> updateActivity(dynamic id, Map<String, dynamic> data) async {
    try {
      final response = await _dio.put('/api/crm/activities/$id', data: data);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در به‌روزرسانی فعالیت',
          displayMessage: response.data['message'] ?? 'خطا در به‌روزرسانی فعالیت',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// حذف فعالیت
  Future<Map<String, dynamic>> deleteActivity(dynamic id) async {
    try {
      final response = await _dio.delete('/api/crm/activities/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در حذف فعالیت',
          displayMessage: response.data['message'] ?? 'خطا در حذف فعالیت',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// دریافت لیست نمایشگاه‌ها
  Future<Map<String, dynamic>> getExhibitions({
    int page = 1,
    int limit = 20,
    String? search,
    String? status,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (search != null) queryParams['search'] = search;
      if (status != null) queryParams['status'] = status;
      if (sortBy != null) queryParams['sort_by'] = sortBy;
      if (sortOrder != null) queryParams['sort_order'] = sortOrder;

      final response = await _dio.get('/api/v1/exhibitions', queryParameters: queryParams);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در دریافت نمایشگاه‌ها',
          displayMessage: response.data['message'] ?? 'خطا در دریافت نمایشگاه‌ها',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// ایجاد نمایشگاه جدید
  Future<Map<String, dynamic>> createExhibition(Map<String, dynamic> data) async {
    try {
      final response = await _dio.post('/api/v1/exhibitions', data: data);
      
      if (response.statusCode == 201 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در ایجاد نمایشگاه',
          displayMessage: response.data['message'] ?? 'خطا در ایجاد نمایشگاه',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// به‌روزرسانی نمایشگاه
  Future<Map<String, dynamic>> updateExhibition(dynamic id, Map<String, dynamic> data) async {
    try {
      final response = await _dio.put('/api/v1/exhibitions/$id', data: data);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در به‌روزرسانی نمایشگاه',
          displayMessage: response.data['message'] ?? 'خطا در به‌روزرسانی نمایشگاه',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// حذف نمایشگاه
  Future<Map<String, dynamic>> deleteExhibition(dynamic id) async {
    try {
      final response = await _dio.delete('/api/v1/exhibitions/$id');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw ApiException(
          message: response.data['message'] ?? 'خطا در حذف نمایشگاه',
          displayMessage: response.data['message'] ?? 'خطا در حذف نمایشگاه',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
}
