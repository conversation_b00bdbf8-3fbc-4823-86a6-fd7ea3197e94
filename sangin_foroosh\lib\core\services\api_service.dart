import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../constants/app_config.dart';
import '../errors/api_exception.dart';
import '../middleware/exhibition_middleware.dart';
import '../../features/exhibition/models/exhibition.dart';

/// سرویس اصلی برای ارتباط با API
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal() {
    _initialize();
  }
  
  late final Dio _dio;
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _tokenKey = 'auth_token';

  /// مقداردهی اولیه سرویس
  void _initialize() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConfig.baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      ),
    );

    // اضافه کردن ExhibitionMiddleware برای مدیریت نمایشگاه فعال
    _dio.interceptors.add(ExhibitionMiddleware());
    
    // اضافه کردن Interceptor برای مدیریت توکن و خطاها
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // اضافه کردن توکن به هدر درخواست
          final token = await getToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          // مدیریت خطای 401 (Unauthorized)
          if (error.response?.statusCode == 401) {
            await clearToken();
            // پاک کردن نمایشگاه فعال در صورت خطای احراز هویت
            await ExhibitionMiddleware.clearActiveExhibition();
            // اینجا می‌توانید کاربر را به صفحه ورود هدایت کنید
            // Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
          }
          handler.next(error);
        },
      ),
    );

    // اضافه کردن Logger برای نمایش درخواست‌ها در کنسول
    _dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
      ),
    );
  }

  /// دریافت instance از Dio
  Dio get dio => _dio;

  /// ذخیره توکن در حافظه امن
  static Future<void> saveToken(String token) async {
    await _storage.write(key: _tokenKey, value: token);
  }

  /// دریافت توکن از حافظه امن
  static Future<String?> getToken() async {
    return await _storage.read(key: _tokenKey);
  }

  /// پاک کردن توکن از حافظه امن
  static Future<void> clearToken() async {
    await _storage.delete(key: _tokenKey);
  }

  /// حذف توکن از حافظه امن (alias برای clearToken)
  static Future<void> removeToken() async {
    await clearToken();
  }

  /// بررسی وجود توکن
  static Future<bool> hasToken() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  /// مدیریت خطاهای Dio و تبدیل آن به ApiException
  ApiException _handleError(DioException e) {
    String errorMessage = 'یک خطای ناشناخته رخ داد.';
    bool isAuthError = false;

    if (e.response != null) {
      // خطا از سمت سرور آمده است
      final responseData = e.response!.data;
      if (responseData is Map && responseData.containsKey('message')) {
        errorMessage = responseData['message'];
      } else {
        errorMessage = 'پاسخ نامعتبر از سرور دریافت شد.';
      }

      // بررسی خطای احراز هویت
      if (e.response!.statusCode == 401) {
        isAuthError = true;
      }
    } else if (e.type == DioExceptionType.connectionTimeout ||
               e.type == DioExceptionType.receiveTimeout ||
               e.type == DioExceptionType.sendTimeout) {
      errorMessage = 'زمان ارتباط با سرور به پایان رسید. لطفاً اتصال اینترنت خود را بررسی کنید.';
    } else if (e.type == DioExceptionType.unknown) {
      errorMessage = 'خطا در ارتباط با سرور. لطفاً اتصال اینترنت خود را بررسی کنید.';
    }

    return ApiException(
      message: e.message ?? errorMessage,
      displayMessage: errorMessage,
      statusCode: e.response?.statusCode,
      isAuthError: isAuthError,
      errors: e.response?.data is Map ? e.response?.data['errors'] : null,
    );
  }

  /// ورود کاربر
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await _dio.post(
        AppConfig.loginEndpoint,
        data: {
          'email': email,
          'password': password,
        },
      );

      // Dio by default throws an exception for non-2xx status codes.
      // The check for success is an additional layer.
      if (response.data['success'] == true) {
        final token = response.data['data']['token'];
        await saveToken(token);
        return response.data;
      } else {
        // If success is false, but status code was 2xx, we should still treat it as an error.
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'عملیات موفق نبود',
          type: DioExceptionType.badResponse,
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  // ===================================================================
  // Task Module
  // ===================================================================

  /// دریافت لیست وظایف
  Future<Map<String, dynamic>> getTasks({
    int page = 1,
    int limit = 20,
    String? search,
    String? status,
    String? priority,
    String? assignedTo,
    String? categoryId,
    String? projectId,
    DateTime? dueDateFrom,
    DateTime? dueDateTo,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (status != null && status.isNotEmpty) queryParams['status'] = status;
      if (priority != null && priority.isNotEmpty) queryParams['priority'] = priority;
      if (assignedTo != null && assignedTo.isNotEmpty) queryParams['assigned_to'] = assignedTo;
      if (categoryId != null && categoryId.isNotEmpty) queryParams['category_id'] = categoryId;
      if (projectId != null && projectId.isNotEmpty) queryParams['project_id'] = projectId;
      if (dueDateFrom != null) queryParams['due_date_from'] = dueDateFrom.toIso8601String();
      if (dueDateTo != null) queryParams['due_date_to'] = dueDateTo.toIso8601String();
      if (sortBy != null && sortBy.isNotEmpty) queryParams['sort_by'] = sortBy;
      if (sortOrder != null && sortOrder.isNotEmpty) queryParams['sort_order'] = sortOrder;
      final response = await _dio.get(
        '/tasks',
        queryParameters: queryParams,
      );
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت اطلاعات وظایف',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
       throw _handleError(e);
     }
   }
- 
+         return response.data;
+       } else {
+         throw DioException(
+           requestOptions: response.requestOptions,
+           response: response,
+           message: response.data['message'] ?? 'خطا در به‌روزرسانی وظیفه',
+         );
+       }
+     } on DioException catch (e) {
+       throw _handleError(e);
+     }
+   }
     } on DioException catch (e) {
       throw _handleError(e);
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در به‌روزرسانی وظیفه',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  /// دریافت اطلاعات یک وظیفه خاص
  Future<Map<String, dynamic>> getTask(String taskId) async {
    try {
      final response = await _dio.get('/tasks/ $taskId');
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data['data'];
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت اطلاعات وظیفه',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// ایجاد وظیفه جدید
  Future<Map<String, dynamic>> createTask(Map<String, dynamic> taskData) async {
    try {
      final response = await _dio.post(
        '/tasks',
        data: taskData,
      );
      if (response.statusCode == 201 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در ایجاد وظیفه',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// به‌روزرسانی وظیفه
  Future<Map<String, dynamic>> updateTask(
    String taskId,
    Map<String, dynamic> taskData,
  ) async {
    try {
      final response = await _dio.put(
        '/tasks/ $taskId',
        data: taskData,
      );
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در به‌روزرسانی وظیفه',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// حذف وظیفه
  Future<Map<String, dynamic>> deleteTask(String taskId) async {
    try {
      final response = await _dio.delete('/tasks/ $taskId');
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در حذف وظیفه',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// تغییر وضعیت وظیفه
  Future<Map<String, dynamic>> updateTaskStatus(
    String taskId,
    String status,
  ) async {
    try {
      final response = await _dio.patch(
        '/tasks/$taskId/status',
        data: {'status': status},
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در تغییر وضعیت وظیفه',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// افزودن نظر به وظیفه
  Future<Map<String, dynamic>> addTaskComment(
    String taskId,
    String content,
  ) async {
    try {
      final response = await _dio.post(
        '/tasks/$taskId/comments',
        data: {'content': content},
      );
      
      if (response.statusCode == 201 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در افزودن نظر',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// آپلود ضمیمه برای وظیفه
  Future<Map<String, dynamic>> uploadTaskAttachment(
    String taskId,
    String filePath,
  ) async {
    try {
      final fileName = filePath.split('/').last;
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath, filename: fileName),
      });
      
      final response = await _dio.post(
        '/tasks/$taskId/attachments',
        data: formData,
      );
      
      if (response.statusCode == 201 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در آپلود ضمیمه',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // ==================== AI Module Methods ====================
  
  /// ارسال پرسش هوشمند (Smart Query)
  Future<Map<String, dynamic>> sendSmartQuery(String query) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai/converse',
        data: {'query': query},
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در ارسال پرسش',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت لیست ایجنت‌های هوش مصنوعی
  Future<Map<String, dynamic>> getAIAgents({
    int page = 1,
    int limit = 20,
    String? search,
    String? status,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (status != null && status.isNotEmpty) queryParams['status'] = status;
      if (sortBy != null && sortBy.isNotEmpty) queryParams['sort_by'] = sortBy;
      if (sortOrder != null && sortOrder.isNotEmpty) queryParams['sort_order'] = sortOrder;
      
      final response = await _dio.get(
        '/api/v1/ai/agents',
        queryParameters: queryParams,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت ایجنت‌ها',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت اطلاعات یک ایجنت خاص
  Future<Map<String, dynamic>> getAIAgent(String agentId) async {
    try {
      final response = await _dio.get('/api/v1/ai/agents/$agentId');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data['data'];
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت اطلاعات ایجنت',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// ارسال پیام به ایجنت خاص
  Future<Map<String, dynamic>> sendMessageToAgent(
    String agentId,
    String message,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai/agents/$agentId/converse',
        data: {'message': message},
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در ارسال پیام',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت تاریخچه گفتگو (Smart Query)
  Future<Map<String, dynamic>> getSmartQueryHistory({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      final response = await _dio.get(
        '/api/v1/ai/conversations/smart-query',
        queryParameters: queryParams,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت تاریخچه',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت تاریخچه گفتگو با ایجنت خاص
  Future<Map<String, dynamic>> getAgentConversationHistory(
    String agentId, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      final response = await _dio.get(
        '/api/v1/ai/conversations/agent/$agentId',
        queryParameters: queryParams,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت تاریخچه گفتگو',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// حذف گفتگو
  Future<Map<String, dynamic>> deleteConversation(String conversationId) async {
    try {
      final response = await _dio.delete('/api/v1/ai/conversations/$conversationId');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در حذف گفتگو',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// پاک کردن تمام گفتگوها
  Future<Map<String, dynamic>> clearAllConversations() async {
    try {
      final response = await _dio.delete('/api/v1/ai/conversations/clear');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در پاک کردن گفتگوها',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// ارسال بازخورد برای پیام
  Future<Map<String, dynamic>> submitMessageFeedback(
    String messageId,
    String feedbackType, {
    String? comment,
  }) async {
    try {
      final data = {
        'feedback_type': feedbackType,
        if (comment != null && comment.isNotEmpty) 'comment': comment,
      };
      
      final response = await _dio.post(
        '/api/v1/ai/messages/$messageId/feedback',
        data: data,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در ارسال بازخورد',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت آمار ایجنت‌ها
  Future<Map<String, dynamic>> getAIAgentsStatistics() async {
    try {
      final response = await _dio.get('/api/v1/ai/agents/statistics');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data['data'];
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت آمار',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// پیش‌بینی قیمت ماشین‌آلات با استفاده از هوش مصنوعی
  Future<Map<String, dynamic>> predictMachineryPrice(Map<String, dynamic> machineryData) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai/predict-price',
        data: machineryData,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در پیش‌بینی قیمت',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت تعاملات CRM با تحلیل احساسات
  Future<Map<String, dynamic>> getCrmInteractionsWithSentiment({
    int page = 1,
    int limit = 20,
    int? contactId,
    String? type,
    String? sentiment,
    DateTime? dateFrom,
    DateTime? dateTo,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (contactId != null) queryParams['contact_id'] = contactId;
      if (type != null && type.isNotEmpty) queryParams['type'] = type;
      if (sentiment != null && sentiment.isNotEmpty) queryParams['sentiment'] = sentiment;
      if (dateFrom != null) queryParams['date_from'] = dateFrom.toIso8601String();
      if (dateTo != null) queryParams['date_to'] = dateTo.toIso8601String();
      
      final response = await _dio.get(
        '/api/v1/crm/interactions',
        queryParameters: queryParams,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت تعاملات',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت ناهنجاری‌های مالی
  Future<Map<String, dynamic>> getFinancialAnomalies({
    int page = 1,
    int limit = 20,
    String? severity,
    String? status,
    String? category,
    DateTime? dateFrom,
    DateTime? dateTo,
    double? minAmount,
    double? maxAmount,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (severity != null && severity.isNotEmpty) queryParams['severity'] = severity;
      if (status != null && status.isNotEmpty) queryParams['status'] = status;
      if (category != null && category.isNotEmpty) queryParams['category'] = category;
      if (dateFrom != null) queryParams['date_from'] = dateFrom.toIso8601String();
      if (dateTo != null) queryParams['date_to'] = dateTo.toIso8601String();
      if (minAmount != null) queryParams['min_amount'] = minAmount;
      if (maxAmount != null) queryParams['max_amount'] = maxAmount;
      
      final response = await _dio.get(
        '/api/v1/accounting/anomalies',
        queryParameters: queryParams,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت ناهنجاری‌های مالی',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// به‌روزرسانی وضعیت ناهنجاری مالی
  Future<Map<String, dynamic>> updateFinancialAnomalyStatus(
    int anomalyId,
    String status,
    {String? notes}
  ) async {
    try {
      final data = {
        'status': status,
        if (notes != null && notes.isNotEmpty) 'notes': notes,
      };
      
      final response = await _dio.patch(
        '/api/v1/accounting/anomalies/$anomalyId/status',
        data: data,
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در به‌روزرسانی وضعیت ناهنجاری',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  // ==================== Exhibition API Methods ====================
  
  /// دریافت لیست نمایشگاه‌های موجود
  Future<List<Exhibition>> getAvailableExhibitions() async {
    try {
      final response = await _dio.get('/api/v1/exhibitions');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> exhibitionsData = response.data['data'];
        return exhibitionsData.map((json) => Exhibition.fromJson(json)).toList();
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت نمایشگاه‌ها',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت اطلاعات یک نمایشگاه خاص
  Future<Exhibition> getExhibition(int exhibitionId) async {
    try {
      final response = await _dio.get('/api/v1/exhibitions/$exhibitionId');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return Exhibition.fromJson(response.data['data']);
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت اطلاعات نمایشگاه',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت نمایشگاه‌های کاربر
  Future<List<Exhibition>> getUserExhibitions() async {
    try {
      final response = await _dio.get('/api/v1/user/exhibitions');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> exhibitionsData = response.data['data'];
        return exhibitionsData.map((json) => Exhibition.fromJson(json)).toList();
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت نمایشگاه‌های کاربر',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// تنظیم نمایشگاه فعال
  Future<Map<String, dynamic>> setActiveExhibition(int exhibitionId) async {
    try {
      final response = await _dio.post(
        '/api/v1/user/active-exhibition',
        data: {'exhibition_id': exhibitionId},
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در تنظیم نمایشگاه فعال',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// دریافت نمایشگاه فعال کاربر
  Future<Exhibition?> getActiveExhibition() async {
    try {
      final response = await _dio.get('/api/v1/user/active-exhibition');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        final exhibitionData = response.data['data'];
        if (exhibitionData != null) {
          return Exhibition.fromJson(exhibitionData);
        }
        return null;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در دریافت نمایشگاه فعال',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// متد عمومی برای درخواست GET
  Future<dynamic> get(String path, {Map<String, dynamic>? queryParameters}) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در درخواست GET',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// متد عمومی برای درخواست POST
  Future<dynamic> post(String path, {dynamic data}) async {
    try {
      final response = await _dio.post(path, data: data);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در درخواست POST',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// متد عمومی برای درخواست PUT
  Future<dynamic> put(String path, {dynamic data}) async {
    try {
      final response = await _dio.put(path, data: data);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در درخواست PUT',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// متد عمومی برای درخواست DELETE
  Future<dynamic> delete(String path) async {
    try {
      final response = await _dio.delete(path);
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return response.data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: response.data['message'] ?? 'خطا در درخواست DELETE',
        );
      }
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// متد عمومی برای مدیریت خطاها که توسط سایر کلاس‌ها قابل استفاده است
  ApiException handleError(dynamic error) {
    if (error is DioException) {
      return _handleError(error);
    } else if (error is ApiException) {
      return error;
    } else {
      return ApiException(
        message: error.toString(),
        displayMessage: 'خطای غیرمنتظره‌ای رخ داد.',
        statusCode: null,
      );
    }
  }
  
  /// دریافت instance از ApiService (برای استفاده در سایر کلاس‌ها)
  static ApiService get instance => _instance;
}