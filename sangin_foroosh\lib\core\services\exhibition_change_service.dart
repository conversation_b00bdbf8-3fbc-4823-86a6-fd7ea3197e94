import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/active_exhibition_provider.dart';
import '../../features/exhibition/providers/exhibition_provider.dart';
import '../../features/machinery/providers/machinery_provider.dart';
import '../../features/machinery/providers/machinery_event_provider.dart';
import '../constants/storage_keys.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// سرویس مدیریت تغییر نمایشگاه فعال و بروزرسانی داده‌ها
class ExhibitionChangeService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  
  /// لیست providerهایی که باید پس از تغییر نمایشگاه بروزرسانی شوند
  static const List<String> _providersToRefresh = [
    'ExhibitionProvider',
    'MachineryProvider', 
    'MachineryEventProvider',
    // می‌توانید providerهای دیگر را اینجا اضافه کنید
  ];
  
  /// تغییر نمایشگاه فعال و بروزرسانی تمام داده‌ها
  static Future<void> changeActiveExhibition(
    BuildContext context,
    int exhibitionId,
  ) async {
    try {
      // دریافت provider نمایشگاه فعال
      final activeExhibitionProvider = Provider.of<ActiveExhibitionProvider>(
        context,
        listen: false,
      );
      
      // پیدا کردن نمایشگاه با ID مشخص شده
      final exhibition = activeExhibitionProvider.availableExhibitions
          .firstWhere((ex) => ex.id == exhibitionId);
      
      // تنظیم نمایشگاه فعال
      await activeExhibitionProvider.setActiveExhibition(exhibition);
      
      // بروزرسانی تمام providerها
      if (context.mounted) {
        await _refreshAllProviders(context);
      }
      
      // پاک کردن کش‌های مربوط به نمایشگاه قبلی
      await _clearExhibitionRelatedCache();
      
    } catch (e) {
      throw Exception('خطا در تغییر نمایشگاه فعال: ${e.toString()}');
    }
  }
  
  /// بروزرسانی تمام providerهای مرتبط
  static Future<void> _refreshAllProviders(BuildContext context) async {
    if (!context.mounted) return;

    try {
      // بروزرسانی ExhibitionProvider
      final exhibitionProvider = Provider.of<ExhibitionProvider>(
        context,
        listen: false,
      );
      await exhibitionProvider.loadExhibitions();

      if (!context.mounted) return;

      // بروزرسانی MachineryProvider
      try {
        final machineryProvider = Provider.of<MachineryProvider>(
          context,
          listen: false,
        );
        await machineryProvider.loadMachineries();
      } catch (e) {
        developer.log('خطا در بروزرسانی MachineryProvider: $e', name: 'ExhibitionChangeService');
      }

      if (!context.mounted) return;

      // بروزرسانی MachineryEventProvider
      try {
        final machineryEventProvider = Provider.of<MachineryEventProvider>(
          context,
          listen: false,
        );
        await machineryEventProvider.loadMachineryEvents();
      } catch (e) {
        developer.log('خطا در بروزرسانی MachineryEventProvider: $e', name: 'ExhibitionChangeService');
      }

      // اینجا می‌توانید providerهای دیگر را اضافه کنید

    } catch (e) {
      developer.log('خطا در بروزرسانی providerها: $e', name: 'ExhibitionChangeService');
    }
  }
  
  /// پاک کردن کش‌های مربوط به نمایشگاه
  static Future<void> _clearExhibitionRelatedCache() async {
    try {
      await _storage.delete(key: StorageKeys.machineryCache);
      await _storage.delete(key: StorageKeys.categoriesCache);
      await _storage.delete(key: StorageKeys.manufacturersCache);
      await _storage.delete(key: StorageKeys.locationsCache);
      await _storage.delete(key: StorageKeys.tempData);
    } catch (e) {
      developer.log('خطا در پاک کردن کش: $e', name: 'ExhibitionChangeService');
    }
  }
  
  /// بروزرسانی داده‌ها پس از تغییر نمایشگاه (بدون تغییر نمایشگاه فعال)
  static Future<void> refreshDataAfterExhibitionChange(
    BuildContext context,
  ) async {
    if (!context.mounted) return;
    await _refreshAllProviders(context);
    await _clearExhibitionRelatedCache();
  }
  
  /// بررسی اینکه آیا نمایشگاه فعال تغییر کرده است
  static Future<bool> hasExhibitionChanged() async {
    try {
      final currentExhibitionJson = await _storage.read(key: StorageKeys.activeExhibition);
      final lastExhibitionId = await _storage.read(key: StorageKeys.lastSelectedExhibitionId);
      
      if (currentExhibitionJson == null || lastExhibitionId == null) {
        return false;
      }
      
      // استخراج ID از JSON فعلی
      final decodedData = jsonDecode(currentExhibitionJson);
      if (decodedData is! Map<String, dynamic>) {
        developer.log('خطا: فرمت ذخیره شده نمایشگاه فعال نامعتبر است.', name: 'ExhibitionChangeService');
        return false;
      }
      final Map<String, dynamic> currentExhibition = decodedData;
      final currentId = currentExhibition['id']?.toString();

      if (currentId != null) {
        return currentId != lastExhibitionId;
      }
      
      return false;
    } catch (e) {
      developer.log('خطا در بررسی تغییر نمایشگاه: $e', name: 'ExhibitionChangeService');
      return false;
    }
  }
  
  /// ذخیره ID نمایشگاه فعال برای مقایسه بعدی
  static Future<void> saveLastSelectedExhibitionId(int exhibitionId) async {
    try {
      await _storage.write(
        key: StorageKeys.lastSelectedExhibitionId,
        value: exhibitionId.toString(),
      );
    } catch (e) {
      developer.log('خطا در ذخیره ID نمایشگاه: $e', name: 'ExhibitionChangeService');
    }
  }
  
  /// دریافت لیست نام providerهایی که باید بروزرسانی شوند
  static List<String> get providersToRefresh => _providersToRefresh;
  
  /// اضافه کردن provider جدید به لیست بروزرسانی
  static void addProviderToRefresh(String providerName) {
    if (!_providersToRefresh.contains(providerName)) {
      // نمی‌توانیم مستقیماً به لیست const اضافه کنیم
      // این متد برای مستندسازی است
      developer.log('برای اضافه کردن $providerName، آن را به لیست _providersToRefresh اضافه کنید', name: 'ExhibitionChangeService');
    }
  }
  
  /// بروزرسانی اجباری تمام داده‌ها (برای استفاده در مواقع خاص)
  static Future<void> forceRefreshAllData(BuildContext context) async {
    try {
      // پاک کردن تمام کش‌ها
      for (final key in StorageKeys.cacheKeys) {
        await _storage.delete(key: key);
      }
      
      // بروزرسانی providerها
      if (context.mounted) {
        await _refreshAllProviders(context);
      }
      
    } catch (e) {
      throw Exception('خطا در بروزرسانی اجباری داده‌ها: ${e.toString()}');
    }
  }
}

/// Mixin برای صفحاتی که نیاز به بروزرسانی پس از تغییر نمایشگاه دارند
mixin ExhibitionAwarePageMixin<T extends StatefulWidget> on State<T> {
  
  @override
  void initState() {
    super.initState();
    _checkExhibitionChange();
  }
  
  /// بررسی تغییر نمایشگاه و بروزرسانی در صورت نیاز
  Future<void> _checkExhibitionChange() async {
    final hasChanged = await ExhibitionChangeService.hasExhibitionChanged();
    if (hasChanged && mounted) {
      await ExhibitionChangeService.refreshDataAfterExhibitionChange(context);
    }
  }
  
  /// متد برای بروزرسانی دستی داده‌ها
  Future<void> refreshData() async {
    if (mounted) {
      await ExhibitionChangeService.refreshDataAfterExhibitionChange(context);
    }
  }
}