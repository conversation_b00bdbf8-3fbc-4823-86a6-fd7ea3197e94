import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';

import '../models/machinery.dart';
import '../providers/machinery_provider.dart';
import '../../../core/services/api_service.dart';
import '../../../core/constants/app_config.dart';
import '../../../core/utils/jalali_formatter.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';

class MachineryFormScreen extends StatefulWidget {
  final Machinery? machinery; // اگر null باشد، حالت ایجاد است، در غیر این صورت حالت ویرایش

  const MachineryFormScreen({Key? key, this.machinery}) : super(key: key);

  @override
  State<MachineryFormScreen> createState() => _MachineryFormScreenState();
}

class _MachineryFormScreenState extends State<MachineryFormScreen> {
  final _formKey = GlobalKey<FormState>();
  int _currentStep = 0;
  bool _isLoading = false;
  String? _error;
  bool _isSubmitting = false;

  // کنترلرهای فرم
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _chassisNumberController = TextEditingController();
  final TextEditingController _engineNumberController = TextEditingController();
  final TextEditingController _manufactureYearController = TextEditingController();
  final TextEditingController _enginePowerController = TextEditingController();
  final TextEditingController _workingHoursController = TextEditingController();
  final TextEditingController _capacityController = TextEditingController();
  final TextEditingController _colorController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _purchasePriceController = TextEditingController();
  final TextEditingController _sellingPriceController = TextEditingController();
  final TextEditingController _purchaseDateController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  // مقادیر انتخاب شده
  dynamic _selectedTypeId;
  dynamic _selectedBrandId;
  dynamic _selectedModelId;
  dynamic _selectedExhibitionId;
  String _selectedStatus = 'available'; // مقدار پیش‌فرض
  DateTime? _purchaseDate;

  // لیست‌های دراپ‌داون
  List<MachineryType> _types = [];
  List<MachineryBrand> _brands = [];
  List<MachineryModel> _models = [];
  List<dynamic> _exhibitions = [];
  List<dynamic> _locations = [];

  // تصاویر
  List<File> _selectedImages = [];
  List<dynamic> _existingImages = [];
  List<int> _imagesToDelete = [];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _chassisNumberController.dispose();
    _engineNumberController.dispose();
    _manufactureYearController.dispose();
    _enginePowerController.dispose();
    _workingHoursController.dispose();
    _capacityController.dispose();
    _colorController.dispose();
    _locationController.dispose();
    _purchasePriceController.dispose();
    _sellingPriceController.dispose();
    _purchaseDateController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final machineryProvider = Provider.of<MachineryProvider>(context, listen: false);
      final apiService = ApiService();

      // بارگذاری انواع ماشین‌آلات
      final typesResult = await apiService.getMachineryCategories();
      final List<dynamic> typesData = typesResult['data'];
      _types = typesData.map((data) => MachineryType.fromJson(data)).toList();

      // بارگذاری برندها
      final brandsResult = await apiService.getMachineryManufacturers();
      final List<dynamic> brandsData = brandsResult['data'];
      _brands = brandsData.map((data) => MachineryBrand.fromJson(data)).toList();

      // بارگذاری موقعیت‌ها
      final locationsResult = await apiService.getMachineryLocations();
      _locations = locationsResult['data'];

      // بارگذاری نمایشگاه‌ها
      final exhibitionsResult = await apiService.getExhibitions();
      _exhibitions = exhibitionsResult['data'];

      // اگر در حالت ویرایش هستیم، مقادیر را پر کنیم
      if (widget.machinery != null) {
        _nameController.text = widget.machinery!.name;
        _chassisNumberController.text = widget.machinery!.chassisNumber ?? '';
        _engineNumberController.text = widget.machinery!.engineNumber ?? '';
        _manufactureYearController.text = widget.machinery!.manufactureYear?.toString() ?? '';
        _enginePowerController.text = widget.machinery!.enginePower?.toString() ?? '';
        _workingHoursController.text = widget.machinery!.workingHours?.toString() ?? '';
        _capacityController.text = widget.machinery!.capacity ?? '';
        _colorController.text = widget.machinery!.color ?? '';
        _locationController.text = widget.machinery!.location ?? '';
        _purchasePriceController.text = widget.machinery!.purchasePrice?.toString() ?? '';
        _sellingPriceController.text = widget.machinery!.sellingPrice?.toString() ?? '';
        _descriptionController.text = widget.machinery!.description ?? '';
        
        if (widget.machinery!.purchaseDate != null) {
          _purchaseDate = widget.machinery!.purchaseDate;
          _purchaseDateController.text = JalaliFormatter.formatDate(_purchaseDate!);
        }
        
        _selectedTypeId = widget.machinery!.type?.id;
        _selectedBrandId = widget.machinery!.brand?.id;
        _selectedStatus = widget.machinery!.status ?? 'available';
        _selectedExhibitionId = widget.machinery!.exhibitionId;
        
        // بارگذاری مدل‌ها بر اساس برند انتخاب شده
        if (_selectedBrandId != null) {
          await _loadModels(_selectedBrandId);
          _selectedModelId = widget.machinery!.model?.id;
        }
        
        // بارگذاری تصاویر موجود
        if (widget.machinery!.images != null) {
          _existingImages = widget.machinery!.images!;
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadModels(dynamic brandId) async {
    try {
      final apiService = ApiService();
      final result = await apiService.getMachineryModels(brandId: brandId);
      final List<dynamic> modelsData = result['data'];
      setState(() {
        _models = modelsData.map((data) => MachineryModel.fromJson(data)).toList();
      });
    } catch (e) {
      // خطا را نمایش می‌دهیم اما اجازه می‌دهیم کاربر به کار خود ادامه دهد
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطا در بارگذاری مدل‌ها: ${e.toString()}')),
      );
    }
  }

  Future<void> _pickImages() async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage();
      
      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images.map((image) => File(image.path)).toList());
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطا در انتخاب تصاویر: ${e.toString()}')),
      );
    }
  }

  Future<void> _takePicture() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.camera);
      
      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطا در گرفتن عکس: ${e.toString()}')),
      );
    }
  }

  void _removeSelectedImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _removeExistingImage(int index) {
    setState(() {
      _imagesToDelete.add(_existingImages[index].id);
      _existingImages.removeAt(index);
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _purchaseDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            primaryColor: Theme.of(context).primaryColor,
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).primaryColor,
            ),
            buttonTheme: const ButtonThemeData(
              textTheme: ButtonTextTheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _purchaseDate = picked;
        _purchaseDateController.text = JalaliFormatter.formatDate(picked);
      });
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
      _error = null;
    });

    try {
      final machineryProvider = Provider.of<MachineryProvider>(context, listen: false);
      
      // ساخت دیتای ماشین
      final Map<String, dynamic> machineryData = {
        'name': _nameController.text,
        'type_id': _selectedTypeId,
        'brand_id': _selectedBrandId,
        'model_id': _selectedModelId,
        'status': _selectedStatus,
        'chassis_number': _chassisNumberController.text.isNotEmpty ? _chassisNumberController.text : null,
        'engine_number': _engineNumberController.text.isNotEmpty ? _engineNumberController.text : null,
        'manufacture_year': _manufactureYearController.text.isNotEmpty ? int.parse(_manufactureYearController.text) : null,
        'engine_power': _enginePowerController.text.isNotEmpty ? int.parse(_enginePowerController.text) : null,
        'working_hours': _workingHoursController.text.isNotEmpty ? int.parse(_workingHoursController.text) : null,
        'capacity': _capacityController.text.isNotEmpty ? _capacityController.text : null,
        'color': _colorController.text.isNotEmpty ? _colorController.text : null,
        'location': _locationController.text.isNotEmpty ? _locationController.text : null,
        'purchase_price': _purchasePriceController.text.isNotEmpty ? int.parse(_purchasePriceController.text) : null,
        'selling_price': _sellingPriceController.text.isNotEmpty ? int.parse(_sellingPriceController.text) : null,
        'purchase_date': _purchaseDate?.toIso8601String(),
        'description': _descriptionController.text.isNotEmpty ? _descriptionController.text : null,
        'exhibition_id': _selectedExhibitionId,
      };

      // اگر تصاویر برای حذف وجود دارد، آنها را اضافه کنیم
      if (_imagesToDelete.isNotEmpty) {
        machineryData['images_to_delete'] = _imagesToDelete;
      }

      // ایجاد FormData برای آپلود تصاویر
      final formData = FormData.fromMap(machineryData);

      // اضافه کردن تصاویر جدید
      for (int i = 0; i < _selectedImages.length; i++) {
        formData.files.add(
          MapEntry(
            'images[]',
            await MultipartFile.fromFile(_selectedImages[i].path),
          ),
        );
      }

      // ارسال درخواست به سرور
      Machinery? result;
      if (widget.machinery == null) {
        // حالت ایجاد
        result = await machineryProvider.createMachinery(formData);
      } else {
        // حالت ویرایش
        result = await machineryProvider.updateMachinery(widget.machinery!.id, formData);
      }

      setState(() {
        _isSubmitting = false;
      });

      if (result != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.machinery == null
                  ? 'ماشین با موفقیت ایجاد شد'
                  : 'ماشین با موفقیت به‌روزرسانی شد',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // برگشت به صفحه قبل با نتیجه موفقیت‌آمیز
      }
    } catch (e) {
      setState(() {
        _isSubmitting = false;
        _error = e.toString();
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطا: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.machinery == null ? 'افزودن ماشین جدید' : 'ویرایش ماشین'),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _error != null && _types.isEmpty // فقط اگر خطا در بارگذاری اولیه باشد
              ? AppErrorWidget(message: _error!, onRetry: _loadInitialData)
              : Form(
                  key: _formKey,
                  child: Stepper(
                    type: StepperType.vertical,
                    currentStep: _currentStep,
                    onStepContinue: () {
                      if (_currentStep < 2) {
                        setState(() {
                          _currentStep += 1;
                        });
                      } else {
                        _submitForm();
                      }
                    },
                    onStepCancel: () {
                      if (_currentStep > 0) {
                        setState(() {
                          _currentStep -= 1;
                        });
                      } else {
                        Navigator.pop(context);
                      }
                    },
                    controlsBuilder: (context, details) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: Row(
                          children: [
                            ElevatedButton(
                              onPressed: _isSubmitting ? null : details.onStepContinue,
                              child: Text(_currentStep == 2 ? 'ثبت' : 'ادامه'),
                            ),
                            const SizedBox(width: 12),
                            TextButton(
                              onPressed: _isSubmitting ? null : details.onStepCancel,
                              child: Text(_currentStep == 0 ? 'انصراف' : 'بازگشت'),
                            ),
                          ],
                        ),
                      );
                    },
                    steps: [
                      _buildBasicInfoStep(),
                      _buildTechnicalInfoStep(),
                      _buildImagesStep(),
                    ],
                  ),
                ),
    );
  }

  Step _buildBasicInfoStep() {
    return Step(
      title: const Text('اطلاعات پایه'),
      content: Column(
        children: [
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(labelText: 'نام ماشین *'),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'لطفاً نام ماشین را وارد کنید';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<dynamic>(
            decoration: const InputDecoration(labelText: 'نوع ماشین *'),
            value: _selectedTypeId,
            items: _types.map((type) {
              return DropdownMenuItem(
                value: type.id,
                child: Text(type.name),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedTypeId = value;
              });
            },
            validator: (value) {
              if (value == null) {
                return 'لطفاً نوع ماشین را انتخاب کنید';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<dynamic>(
            decoration: const InputDecoration(labelText: 'برند *'),
            value: _selectedBrandId,
            items: _brands.map((brand) {
              return DropdownMenuItem(
                value: brand.id,
                child: Text(brand.name),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedBrandId = value;
                _selectedModelId = null; // ریست کردن مدل
              });
              _loadModels(value); // بارگذاری مدل‌های مرتبط با برند
            },
            validator: (value) {
              if (value == null) {
                return 'لطفاً برند را انتخاب کنید';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<dynamic>(
            decoration: const InputDecoration(labelText: 'مدل'),
            value: _selectedModelId,
            items: _models.map((model) {
              return DropdownMenuItem(
                value: model.id,
                child: Text(model.name),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedModelId = value;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<dynamic>(
            decoration: const InputDecoration(labelText: 'نمایشگاه'),
            value: _selectedExhibitionId,
            items: [
              const DropdownMenuItem(
                value: null,
                child: Text('بدون نمایشگاه'),
              ),
              ..._exhibitions.map((exhibition) {
                return DropdownMenuItem(
                  value: exhibition['id'],
                  child: Text(exhibition['name']),
                );
              }).toList(),
            ],
            onChanged: (value) {
              setState(() {
                _selectedExhibitionId = value;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(labelText: 'وضعیت *'),
            value: _selectedStatus,
            items: const [
              DropdownMenuItem(value: 'available', child: Text('موجود')),
              DropdownMenuItem(value: 'sold', child: Text('فروخته شده')),
              DropdownMenuItem(value: 'reserved', child: Text('رزرو شده')),
              DropdownMenuItem(value: 'under_repair', child: Text('در حال تعمیر')),
              DropdownMenuItem(value: 'in_exhibition', child: Text('در نمایشگاه')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedStatus = value!;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'لطفاً وضعیت را انتخاب کنید';
              }
              return null;
            },
          ),
        ],
      ),
      isActive: _currentStep >= 0,
      state: _currentStep > 0 ? StepState.complete : StepState.indexed,
    );
  }

  Step _buildTechnicalInfoStep() {
    return Step(
      title: const Text('مشخصات فنی'),
      content: Column(
        children: [
          TextFormField(
            controller: _chassisNumberController,
            decoration: const InputDecoration(labelText: 'شماره شاسی'),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _engineNumberController,
            decoration: const InputDecoration(labelText: 'شماره موتور'),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _manufactureYearController,
            decoration: const InputDecoration(labelText: 'سال ساخت'),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _enginePowerController,
            decoration: const InputDecoration(labelText: 'قدرت موتور (اسب بخار)'),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _workingHoursController,
            decoration: const InputDecoration(labelText: 'ساعت کارکرد'),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _capacityController,
            decoration: const InputDecoration(labelText: 'ظرفیت'),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _colorController,
            decoration: const InputDecoration(labelText: 'رنگ'),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(labelText: 'موقعیت'),
            value: _locationController.text.isNotEmpty ? _locationController.text : null,
            items: [
              ..._locations.map((location) {
                return DropdownMenuItem(
                  value: location,
                  child: Text(location),
                );
              }).toList(),
            ],
            onChanged: (value) {
              if (value != null) {
                _locationController.text = value;
              }
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _purchasePriceController,
            decoration: const InputDecoration(labelText: 'قیمت خرید (تومان)'),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _sellingPriceController,
            decoration: const InputDecoration(labelText: 'قیمت فروش (تومان)'),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _purchaseDateController,
            decoration: const InputDecoration(
              labelText: 'تاریخ خرید',
              suffixIcon: Icon(Icons.calendar_today),
            ),
            readOnly: true,
            onTap: () => _selectDate(context),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(labelText: 'توضیحات'),
            maxLines: 3,
          ),
        ],
      ),
      isActive: _currentStep >= 1,
      state: _currentStep > 1 ? StepState.complete : StepState.indexed,
    );
  }

  Step _buildImagesStep() {
    return Step(
      title: const Text('تصاویر و اسناد'),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: _pickImages,
                icon: const Icon(Icons.photo_library),
                label: const Text('انتخاب از گالری'),
              ),
              ElevatedButton.icon(
                onPressed: _takePicture,
                icon: const Icon(Icons.camera_alt),
                label: const Text('گرفتن عکس'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_error != null && _isSubmitting) ...[  
            Container(
              padding: const EdgeInsets.all(8),
              color: Colors.red.shade100,
              child: Text(
                _error!,
                style: const TextStyle(color: Colors.red),
              ),
            ),
            const SizedBox(height: 16),
          ],
          if (_existingImages.isNotEmpty) ...[  
            const Text(
              'تصاویر موجود:',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 8),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: _existingImages.length,
              itemBuilder: (context, index) {
                final image = _existingImages[index];
                return Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          '${AppConfig.baseUrl}${image.url}',
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                        ),
                      ),
                    ),
                    Positioned(
                      top: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () => _removeExistingImage(index),
                        child: Container(
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
          ],
          if (_selectedImages.isNotEmpty) ...[  
            const Text(
              'تصاویر انتخاب شده:',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 8),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _selectedImages[index],
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                        ),
                      ),
                    ),
                    Positioned(
                      top: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () => _removeSelectedImage(index),
                        child: Container(
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ] else if (_existingImages.isEmpty) ...[  
            const Center(
              child: Text(
                'هیچ تصویری انتخاب نشده است',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ],
      ),
      isActive: _currentStep >= 2,
      state: StepState.indexed,
    );
  }
}