import 'package:flutter/material.dart';
import '../../../shared/models/machinery.dart';
import '../../../core/services/api_service.dart';
import '../repositories/machinery_repository.dart';

class MachineryProvider with ChangeNotifier {
  final MachineryRepository _machineryRepository;

  MachineryProvider({required ApiService apiService})
      : _machineryRepository = MachineryRepository(apiService: apiService);

  List<Machinery> _machineries = [];
  List<String> _categories = [];
  List<String> _manufacturers = [];
  List<String> _locations = [];
  Map<String, dynamic>? _statistics;
  bool _isLoading = false;
  String? _error;
  bool _hasMore = true;
  int _currentPage = 1;
  final int _pageSize = 20;

  // Getters
  List<Machinery> get machineries => _machineries;
  List<String> get categories => _categories;
  List<String> get manufacturers => _manufacturers;
  List<String> get locations => _locations;
  Map<String, dynamic>? get statistics => _statistics;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMore => _hasMore;
  
  // Load machineries with pagination
  Future<void> loadMachineries({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _machineries.clear();
    }
    
    if (_isLoading || !_hasMore) return;
    
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _machineryRepository.getMachineries(
        page: _currentPage,
        perPage: _pageSize,
      );
      
      final List<Machinery> newMachineries = result['machineries'];

      if (refresh) {
        _machineries = newMachineries;
      } else {
        _machineries.addAll(newMachineries);
      }

      _hasMore = newMachineries.length == _pageSize;
      if (_hasMore) {
        _currentPage++;
      }

    } catch (e) {
      _error = 'خطا در اتصال به سرور: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Load more machineries for pagination
  Future<void> loadMoreMachineries() async {
    if (!_hasMore || _isLoading) return;
    await loadMachineries();
  }
  
  // Refresh machineries
  Future<void> refreshMachineries() async {
    await loadMachineries(refresh: true);
  }
  
  // TODO: Implement search functionality in the repository first
  /*
  Future<void> searchMachineries(...) async { ... }
  */

  // TODO: Implement category, manufacturer, location, and statistics loading in the repository
  /*
  Future<void> loadCategories() async { ... }
  Future<void> loadManufacturers() async { ... }
  Future<void> loadLocations() async { ... }
  Future<void> loadStatistics() async { ... }
  */
  
  // Get machinery by ID
  Future<Machinery?> getMachinery(int id) async {
    _setLoading(true);
    _clearError();
    try {
      final machinery = await _machineryRepository.getMachinery(id);
      _setLoading(false);
      return machinery;
    } catch (e) {
      debugPrint('Error getting machinery: $e');
      return null;
    }
  }
  
  // Add machinery to favorites (local state)
  void toggleFavorite(int machineryId) {
    final index = _machineries.indexWhere((m) => m.id == machineryId);
    if (index != -1) {
      // This would typically involve an API call to save favorites
      notifyListeners();
    }
  }
  
  // Filter machineries by status
  List<Machinery> getMachineriesByStatus(String status) {
    return _machineries.where((m) => m.status.toLowerCase() == status.toLowerCase()).toList();
  }
  
  // Filter machineries by type
  List<Machinery> getMachineriesByType(String type) {
    return _machineries.where((m) => m.type == type).toList();
  }
  
  // Get available machineries
  List<Machinery> get availableMachineries {
    return _machineries.where((m) => m.isAvailable).toList();
  }
  
  // Get unavailable machineries
  List<Machinery> get unavailableMachineries {
    return _machineries.where((m) => !m.isAvailable).toList();
  }
  
  // Public method to clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
  
  // Clear all data
  void clear() {
    _machineries.clear();
    _categories.clear();
    _manufacturers.clear();
    _locations.clear();
    _statistics = null;
    _error = null;
    _isLoading = false;
    _hasMore = true;
    _currentPage = 1;
    notifyListeners();
  }

  Future<bool> createMachinery(Map<String, dynamic> data) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final newMachinery = await _machineryRepository.createMachinery(data);
      _machineries.insert(0, newMachinery);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'خطا در ایجاد ماشین: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> updateMachinery(int id, Map<String, dynamic> data) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final updatedMachinery = await _machineryRepository.updateMachinery(id, data);
      final index = _machineries.indexWhere((m) => m.id == id);
      if (index != -1) {
        _machineries[index] = updatedMachinery;
      }
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'خطا در به‌روزرسانی ماشین: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> deleteMachinery(int id) async {
    _machineries.removeWhere((m) => m.id == id);
    notifyListeners();
    try {
      await _machineryRepository.deleteMachinery(id);
    } catch (e) {
      // Re-add if delete fails
      // For a better UX, you might want to handle this differently
      // e.g., show an error and allow retry
      loadMachineries(refresh: true);
      throw Exception('Failed to delete machinery on the server.');
    }
  }
}