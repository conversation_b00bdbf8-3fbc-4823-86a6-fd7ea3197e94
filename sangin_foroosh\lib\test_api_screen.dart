import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'features/auth/providers/auth_provider.dart';
import 'core/services/api_service.dart';
import 'core/constants/app_colors.dart';


class TestApiScreen extends StatefulWidget {
  const TestApiScreen({super.key});

  @override
  State<TestApiScreen> createState() => _TestApiScreenState();
}

class _TestApiScreenState extends State<TestApiScreen> {
  final ApiService _apiService = ApiService();
  String _result = 'نتیجه اینجا نمایش داده می‌شود';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'تست API',
          style: TextStyle(
            fontFamily: 'IranSansX',
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'وضعیت احراز هویت:',
                          style: const TextStyle(
                            fontFamily: 'IranSansX',
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          authProvider.isLoggedIn ? 'وارد شده' : 'وارد نشده',
                          style: TextStyle(
                            fontFamily: 'IranSansX',
                            color: authProvider.isLoggedIn ? AppColors.success : AppColors.error,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (authProvider.user != null) ...[
                          const SizedBox(height: 8),
                          Text(
                            'کاربر: ${authProvider.user!.name}',
                            style: const TextStyle(
                              fontFamily: 'IranSansX',
                            ),
                          ),
                          Text(
                            'ایمیل: ${authProvider.user!.email}',
                            style: const TextStyle(
                              fontFamily: 'IranSansX',
                            ),
                          ),
                        ]
                      ],
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testGetMachineries,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'تست دریافت ماشین‌آلات',
                      style: TextStyle(
                        fontFamily: 'IranSansX',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testGetStatistics,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.secondary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'تست آمار',
                      style: TextStyle(
                        fontFamily: 'IranSansX',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testGetCategories,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.accent,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'تست دسته‌بندی‌ها',
                      style: TextStyle(
                        fontFamily: 'IranSansX',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testGetProfile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.info,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'تست پروفایل',
                      style: TextStyle(
                        fontFamily: 'IranSansX',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              ),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'نتیجه:',
                        style: TextStyle(
                          fontFamily: 'IranSansX',
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _result,
                            style: const TextStyle(
                              fontFamily: 'IranSansX',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testGetMachineries() async {
    setState(() {
      _isLoading = true;
      _result = 'در حال دریافت ماشین‌آلات...';
    });

    try {
      final response = await _apiService.getMachineries(page: 1, perPage: 5);
      setState(() {
        _result = 'موفقیت!\n\nپاسخ:\n${_formatJson(response)}';
      });
    } catch (e) {
      setState(() {
        _result = 'خطا:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testGetStatistics() async {
    setState(() {
      _isLoading = true;
      _result = 'در حال دریافت آمار...';
    });

    try {
      final response = await _apiService.getMachineryStatistics();
      setState(() {
        _result = 'موفقیت!\n\nپاسخ:\n${_formatJson(response)}';
      });
    } catch (e) {
      setState(() {
        _result = 'خطا:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testGetCategories() async {
    setState(() {
      _isLoading = true;
      _result = 'در حال دریافت دسته‌بندی‌ها...';
    });

    try {
      final response = await _apiService.getMachineryCategories();
      setState(() {
        _result = 'موفقیت!\n\nپاسخ:\n${_formatJson(response)}';
      });
    } catch (e) {
      setState(() {
        _result = 'خطا:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testGetProfile() async {
    setState(() {
      _isLoading = true;
      _result = 'در حال دریافت پروفایل...';
    });

    try {
      final response = await _apiService.getUserProfile();
      setState(() {
        _result = 'موفقیت!\n\nپاسخ:\n${_formatJson(response)}';
      });
    } catch (e) {
      setState(() {
        _result = 'خطا:\n$e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatJson(dynamic json) {
    if (json is Map) {
      String result = '';
      json.forEach((key, value) {
        result += '$key: $value\n';
      });
      return result;
    }
    return json.toString();
  }
}