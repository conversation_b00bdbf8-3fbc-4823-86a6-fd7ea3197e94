import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/security_provider.dart';
import '../models/permission.dart';
import '../widgets/permission_list_widget.dart';
import '../widgets/permission_gate.dart';
import 'permission_details_screen.dart';

class PermissionsManagementScreen extends StatefulWidget {
  static const String routeName = '/permissions-management';
  
  const PermissionsManagementScreen({super.key});

  @override
  State<PermissionsManagementScreen> createState() => _PermissionsManagementScreenState();
}

class _PermissionsManagementScreenState extends State<PermissionsManagementScreen> {
  bool _isInit = false;
  bool _isLoading = false;
  String? _searchQuery;
  List<Permission> _filteredPermissions = [];
  String? _selectedGroup;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInit) {
      _loadData();
      _isInit = true;
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<SecurityProvider>(context, listen: false).loadInitialData();
      _filterPermissions();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطا در بارگذاری اطلاعات: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterPermissions() {
    final securityProvider = Provider.of<SecurityProvider>(context, listen: false);
    final allPermissions = securityProvider.permissions;

    // ابتدا فیلتر بر اساس گروه
    var groupFiltered = allPermissions;
    if (_selectedGroup != null) {
      groupFiltered = allPermissions.where((permission) {
        return permission.group == _selectedGroup;
      }).toList();
    }

    // سپس فیلتر بر اساس جستجو
    if (_searchQuery == null || _searchQuery!.isEmpty) {
      _filteredPermissions = List.from(groupFiltered);
    } else {
      final query = _searchQuery!.toLowerCase();
      _filteredPermissions = groupFiltered.where((permission) {
        return permission.name.toLowerCase().contains(query) ||
            (permission.displayName?.toLowerCase().contains(query) ?? false) ||
            (permission.description?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    setState(() {});
  }

  List<String> _getAvailableGroups() {
    final securityProvider = Provider.of<SecurityProvider>(context, listen: false);
    final allPermissions = securityProvider.permissions;
    
    final Set<String> groups = {};
    for (var permission in allPermissions) {
      if (permission.group != null) {
        groups.add(permission.group!);
      }
    }
    
    final List<String> sortedGroups = groups.toList();
    sortedGroups.sort();
    return sortedGroups;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مدیریت دسترسی‌ها'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'بارگذاری مجدد',
          ),
          // دکمه افزودن دسترسی جدید - فقط برای کاربران دارای دسترسی
          PermissionGate(
            permissionName: 'permissions.create',
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: () {
                // در اینجا می‌توان به صفحه افزودن دسترسی جدید هدایت کرد
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('این قابلیت هنوز پیاده‌سازی نشده است')),
                );
              },
              tooltip: 'افزودن دسترسی جدید',
            ),
            fallback: const SizedBox.shrink(),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildSearchBar(),
                _buildGroupFilter(),
                Expanded(
                  child: Consumer<SecurityProvider>(
                    builder: (ctx, securityProvider, _) {
                      if (securityProvider.error != null) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'خطا در بارگذاری اطلاعات: ${securityProvider.error}',
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadData,
                                child: const Text('تلاش مجدد'),
                              ),
                            ],
                          ),
                        );
                      }

                      if (_filteredPermissions.isEmpty) {
                        return const Center(
                          child: Text('هیچ دسترسی یافت نشد.'),
                        );
                      }

                      return _buildPermissionsList();
                    },
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'جستجوی دسترسی...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          suffixIcon: _searchQuery != null && _searchQuery!.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    setState(() {
                      _searchQuery = null;
                    });
                    _filterPermissions();
                  },
                )
              : null,
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
          _filterPermissions();
        },
      ),
    );
  }

  Widget _buildGroupFilter() {
    final groups = _getAvailableGroups();
    
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
      child: Row(
        children: [
          const Text('فیلتر بر اساس گروه:'),
          const SizedBox(width: 16),
          Expanded(
            child: DropdownButtonFormField<String?>(
              value: _selectedGroup,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              hint: const Text('همه گروه‌ها'),
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('همه گروه‌ها'),
                ),
                ...groups.map((group) => DropdownMenuItem<String?>(
                      value: group,
                      child: Text(group),
                    )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedGroup = value;
                });
                _filterPermissions();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredPermissions.length,
      itemBuilder: (ctx, index) {
        final permission = _filteredPermissions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text(
              permission.displayNameOrName,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  permission.name,
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
                if (permission.description != null)
                  Text(
                    permission.description!,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
            leading: const Icon(Icons.security),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // دکمه ویرایش - فقط برای کاربران دارای دسترسی
                PermissionGate(
                  permissionName: 'permissions.edit',
                  child: IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      // در اینجا می‌توان به صفحه ویرایش دسترسی هدایت کرد
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('این قابلیت هنوز پیاده‌سازی نشده است')),
                      );
                    },
                    tooltip: 'ویرایش دسترسی',
                  ),
                  fallback: const SizedBox.shrink(),
                ),
                // دکمه حذف - فقط برای کاربران دارای دسترسی
                PermissionGate(
                  permissionName: 'permissions.delete',
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () {
                      // در اینجا می‌توان عملیات حذف دسترسی را انجام داد
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('این قابلیت هنوز پیاده‌سازی نشده است')),
                      );
                    },
                    tooltip: 'حذف دسترسی',
                  ),
                  fallback: const SizedBox.shrink(),
                ),
              ],
            ),
            onTap: () {
              // هدایت به صفحه جزئیات دسترسی
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PermissionDetailsScreen(permission: permission),
                ),
              );
            },
          ),
        );
      },
    );
  }
}