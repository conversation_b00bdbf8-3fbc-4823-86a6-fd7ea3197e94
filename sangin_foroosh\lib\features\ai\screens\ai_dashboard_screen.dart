import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:fl_chart/fl_chart.dart';

import '../providers/ai_provider.dart';
import '../widgets/sentiment_icon.dart';
import 'financial_anomalies_screen.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../core/utils/jalali_formatter.dart';

class AiDashboardScreen extends StatefulWidget {
  const AiDashboardScreen({super.key});

  @override
  State<AiDashboardScreen> createState() => _AiDashboardScreenState();
}

class _AiDashboardScreenState extends State<AiDashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDashboardData();
    });
  }

  Future<void> _loadDashboardData() async {
    final provider = Provider.of<AIProvider>(context, listen: false);
    await Future.wait([
      provider.loadCrmSentimentAnalysis(),
      provider.loadFinancialAnomalies(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('داشبورد هوش مصنوعی'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(TablerIcons.refresh),
            onPressed: _loadDashboardData,
          ),
        ],
      ),
      body: Consumer<AIProvider>(
        builder: (context, provider, child) {
          return RefreshIndicator(
            onRefresh: _loadDashboardData,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // کارت‌های خلاصه
                  _buildSummaryCards(provider),
                  const SizedBox(height: 24),
                  
                  // تحلیل احساسات مشتریان
                  _buildSentimentAnalysisSection(provider),
                  const SizedBox(height: 24),
                  
                  // ناهنجاری‌های مالی
                  _buildFinancialAnomaliesSection(provider),
                  const SizedBox(height: 24),
                  
                  // قابلیت‌های هوش مصنوعی
                  _buildAiFeaturesSection(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSummaryCards(AIProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'خلاصه وضعیت',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'تعاملات مثبت',
                provider.positiveSentimentCount.toString(),
                TablerIcons.mood_happy,
                Colors.green,
                provider.isLoadingSentiment,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'تعاملات منفی',
                provider.negativeSentimentCount.toString(),
                TablerIcons.mood_sad,
                Colors.red,
                provider.isLoadingSentiment,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'ناهنجاری‌های مالی',
                provider.totalAnomalies.toString(),
                TablerIcons.alert_triangle,
                Colors.orange,
                provider.isLoadingAnomalies,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'ناهنجاری‌های حل نشده',
                provider.unresolvedAnomalies.toString(),
                TablerIcons.exclamation_circle,
                Colors.red,
                provider.isLoadingAnomalies,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
    bool isLoading,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const Spacer(),
                if (isLoading)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              isLoading ? '...' : value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSentimentAnalysisSection(AIProvider provider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  TablerIcons.brain,
                  size: 24,
                  color: Colors.blue,
                ),
                const SizedBox(width: 8),
                const Text(
                  'تحلیل احساسات مشتریان',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // هدایت به صفحه جزئیات تحلیل احساسات
                    Navigator.pushNamed(context, '/crm/sentiment-analysis');
                  },
                  child: const Text('مشاهده همه'),
                ),
              ],
            ),
            const Divider(),
            if (provider.isLoadingSentiment)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (provider.sentimentError != null)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      const Icon(
                        TablerIcons.alert_circle,
                        size: 48,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        provider.sentimentError!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.red),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => provider.loadCrmSentimentAnalysis(),
                        child: const Text('تلاش مجدد'),
                      ),
                    ],
                  ),
                ),
              )
            else
              Column(
                children: [
                  // نمودار دایره‌ای احساسات
                  SizedBox(
                    height: 200,
                    child: _buildSentimentPieChart(provider),
                  ),
                  const SizedBox(height: 16),
                  // آمار احساسات
                  SentimentStatistics(
                    positiveCount: provider.positiveSentimentCount,
                    negativeCount: provider.negativeSentimentCount,
                    neutralCount: provider.neutralSentimentCount,
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSentimentPieChart(AIProvider provider) {
    final total = provider.positiveSentimentCount +
        provider.negativeSentimentCount +
        provider.neutralSentimentCount;

    if (total == 0) {
      return const Center(
        child: Text(
          'داده‌ای برای نمایش وجود ندارد',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return PieChart(
      PieChartData(
        sections: [
          if (provider.positiveSentimentCount > 0)
            PieChartSectionData(
              value: provider.positiveSentimentCount.toDouble(),
              color: Colors.green,
              title: '${((provider.positiveSentimentCount / total) * 100).toStringAsFixed(1)}%',
              radius: 60,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          if (provider.negativeSentimentCount > 0)
            PieChartSectionData(
              value: provider.negativeSentimentCount.toDouble(),
              color: Colors.red,
              title: '${((provider.negativeSentimentCount / total) * 100).toStringAsFixed(1)}%',
              radius: 60,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          if (provider.neutralSentimentCount > 0)
            PieChartSectionData(
              value: provider.neutralSentimentCount.toDouble(),
              color: Colors.grey,
              title: '${((provider.neutralSentimentCount / total) * 100).toStringAsFixed(1)}%',
              radius: 60,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
        ],
        centerSpaceRadius: 40,
        sectionsSpace: 2,
      ),
    );
  }

  Widget _buildFinancialAnomaliesSection(AIProvider provider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  TablerIcons.shield_exclamation,
                  size: 24,
                  color: Colors.orange,
                ),
                const SizedBox(width: 8),
                const Text(
                  'ناهنجاری‌های مالی',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const FinancialAnomaliesScreen(),
                      ),
                    );
                  },
                  child: const Text('مشاهده همه'),
                ),
              ],
            ),
            const Divider(),
            if (provider.isLoadingAnomalies)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (provider.anomaliesError != null)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      const Icon(
                        TablerIcons.alert_circle,
                        size: 48,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        provider.anomaliesError!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.red),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => provider.loadFinancialAnomalies(),
                        child: const Text('تلاش مجدد'),
                      ),
                    ],
                  ),
                ),
              )
            else if (provider.financialAnomalies.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      Icon(
                        TablerIcons.shield_check,
                        size: 48,
                        color: Colors.green,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'هیچ ناهنجاری مالی شناسایی نشده است',
                        style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              Column(
                children: [
                  // آمار کلی ناهنجاری‌ها
                  Row(
                    children: [
                      Expanded(
                        child: _buildAnomalyStatCard(
                          'کل ناهنجاری‌ها',
                          provider.totalAnomalies.toString(),
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildAnomalyStatCard(
                          'حل نشده',
                          provider.unresolvedAnomalies.toString(),
                          Colors.red,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildAnomalyStatCard(
                          'بحرانی',
                          provider.criticalAnomalies.toString(),
                          Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // لیست آخرین ناهنجاری‌ها
                  ...provider.financialAnomalies.take(3).map(
                    (anomaly) => Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _getAnomalySeverityColor(anomaly.severity).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _getAnomalySeverityColor(anomaly.severity).withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 4,
                            height: 40,
                            decoration: BoxDecoration(
                              color: _getAnomalySeverityColor(anomaly.severity),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  anomaly.flagReason,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'مبلغ: ${JalaliFormatter.formatCurrency(anomaly.amount)}',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getAnomalySeverityColor(anomaly.severity),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getAnomalySeverityLabel(anomaly.severity),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnomalyStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAiFeaturesSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  TablerIcons.robot,
                  size: 24,
                  color: Colors.purple,
                ),
                const SizedBox(width: 8),
                const Text(
                  'قابلیت‌های هوش مصنوعی',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
              children: [
                _buildFeatureCard(
                  'پرسش هوشمند',
                  'چت با هوش مصنوعی',
                  TablerIcons.message_circle,
                  Colors.blue,
                  () {
                    Navigator.pushNamed(context, '/smart-query');
                  },
                ),
                _buildFeatureCard(
                  'دستیاران هوشمند',
                  'تعامل با ایجنت‌های AI',
                  TablerIcons.robot,
                  Colors.purple,
                  () {
                    Navigator.pushNamed(context, '/ai-agents');
                  },
                ),
                _buildFeatureCard(
                  'پیش‌بینی قیمت',
                  'تخمین قیمت ماشین‌آلات',
                  TablerIcons.trending_up,
                  Colors.green,
                  () {
                    Navigator.pushNamed(context, '/machinery');
                  },
                ),
                _buildFeatureCard(
                  'تحلیل احساسات',
                  'بررسی نظرات مشتریان',
                  TablerIcons.mood_happy,
                  Colors.teal,
                  () {
                    Navigator.pushNamed(context, '/crm');
                  },
                ),
                _buildFeatureCard(
                  'شناسایی ناهنجاری',
                  'کشف تراکنش‌های مشکوک',
                  TablerIcons.shield_exclamation,
                  Colors.orange,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const FinancialAnomaliesScreen(),
                      ),
                    );
                  },
                ),
                _buildFeatureCard(
                  'گزارش‌های هوشمند',
                  'تحلیل‌های پیشرفته',
                  TablerIcons.chart_bar,
                  Colors.indigo,
                  () {
                    // TODO: پیاده‌سازی گزارش‌های هوشمند
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('این قابلیت در نسخه‌های آینده اضافه خواهد شد'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getAnomalySeverityColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'medium':
        return Colors.yellow;
      case 'low':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getAnomalySeverityLabel(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'بحرانی';
      case 'high':
        return 'بالا';
      case 'medium':
        return 'متوسط';
      case 'low':
        return 'پایین';
      default:
        return severity;
    }
  }
}