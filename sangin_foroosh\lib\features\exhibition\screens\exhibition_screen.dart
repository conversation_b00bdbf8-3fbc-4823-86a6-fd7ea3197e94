import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../shared/models/exhibition.dart';
import '../providers/exhibition_provider.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../core/utils/jalali_formatter.dart';

class ExhibitionScreen extends StatefulWidget {
  const ExhibitionScreen({super.key});

  @override
  State<ExhibitionScreen> createState() => _ExhibitionScreenState();
}

class _ExhibitionScreenState extends State<ExhibitionScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      _loadMoreExhibitions();
    }
  }

  Future<void> _loadData() async {
    final provider = Provider.of<ExhibitionProvider>(context, listen: false);
    await provider.loadExhibitions(refresh: true);
  }

  Future<void> _loadMoreExhibitions() async {
    final provider = Provider.of<ExhibitionProvider>(context, listen: false);
    if (!provider.isLoading && provider.hasMorePages) {
      await provider.loadExhibitions();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مدیریت نمایشگاه‌ها'),
      ),
      body: Consumer<ExhibitionProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.exhibitions.isEmpty) {
            return const LoadingWidget();
          }

          if (provider.error != null && provider.exhibitions.isEmpty) {
            return AppErrorWidget(
              error: provider.error!,
              onRetry: _loadData,
            );
          }

          if (provider.exhibitions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.event_busy, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  const Text('هیچ نمایشگاهی یافت نشد'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _showExhibitionDialog(),
                    child: const Text('افزودن نمایشگاه جدید'),
                  ),
                ],
              ),
            );
          }

          return Stack(
            children: [
              RefreshIndicator(
                onRefresh: _loadData,
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: provider.exhibitions.length + (provider.hasMorePages ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == provider.exhibitions.length) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final exhibition = provider.exhibitions[index];
                    return _buildExhibitionCard(exhibition);
                  },
                ),
              ),
              if (provider.isLoading && provider.exhibitions.isNotEmpty)
                const Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: LinearProgressIndicator(),
                ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showExhibitionDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildExhibitionCard(Exhibition exhibition) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: exhibition.isActive ? Colors.green.withOpacity(0.2) : Colors.grey.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.event,
                    color: exhibition.isActive ? Colors.green : Colors.grey,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        exhibition.name,
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      if (exhibition.description != null && exhibition.description!.isNotEmpty) ...[  
                        const SizedBox(height: 4),
                        Text(
                          exhibition.description!,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(color: Colors.grey),
                        ),
                      ],
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: exhibition.isActive ? Colors.green.withOpacity(0.2) : Colors.grey.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    exhibition.isActive ? 'فعال' : 'غیرفعال',
                    style: TextStyle(
                      color: exhibition.isActive ? Colors.green : Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (exhibition.address != null && exhibition.address!.isNotEmpty) ...[  
              Row(
                children: [
                  const Icon(Icons.location_on, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      exhibition.address!,
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            if (exhibition.phone != null && exhibition.phone!.isNotEmpty) ...[  
              Row(
                children: [
                  const Icon(Icons.phone, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    exhibition.phone!,
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            if (exhibition.manager != null && exhibition.manager!.isNotEmpty) ...[  
              Row(
                children: [
                  const Icon(Icons.person, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'مدیر: ${exhibition.manager}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            Row(
              children: [
                const Icon(Icons.update, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  'آخرین به‌روزرسانی: ${JalaliFormatter.formatDate(exhibition.updatedAt)}',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
            if (exhibition.machineries != null && exhibition.machineries!.isNotEmpty) ...[  
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.precision_manufacturing, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'تعداد ماشین‌آلات: ${exhibition.machineryCount}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  onPressed: () => _showExhibitionDialog(exhibition: exhibition),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _confirmDeleteExhibition(exhibition),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }



  Future<void> _showExhibitionDialog({Exhibition? exhibition}) async {
    // اگر exhibition نال باشد، حالت افزودن است، در غیر این صورت حالت ویرایش
    final isEditing = exhibition != null;
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: exhibition?.name ?? '');
    final descriptionController = TextEditingController(text: exhibition?.description ?? '');
    final addressController = TextEditingController(text: exhibition?.address ?? '');
    final phoneController = TextEditingController(text: exhibition?.phone ?? '');
    final managerController = TextEditingController(text: exhibition?.manager ?? '');
    
    bool isActive = exhibition?.isActive ?? true;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'ویرایش نمایشگاه' : 'افزودن نمایشگاه جدید'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(labelText: 'نام نمایشگاه *'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'لطفاً نام نمایشگاه را وارد کنید';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: descriptionController,
                  decoration: const InputDecoration(labelText: 'توضیحات'),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: addressController,
                  decoration: const InputDecoration(labelText: 'آدرس'),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: phoneController,
                  decoration: const InputDecoration(labelText: 'تلفن'),
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: managerController,
                  decoration: const InputDecoration(labelText: 'مدیر'),
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text('وضعیت نمایشگاه'),
                  subtitle: Text(isActive ? 'فعال' : 'غیرفعال'),
                  value: isActive,
                  activeColor: Colors.green,
                  onChanged: (value) {
                    setState(() {
                      isActive = value;
                    });
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('انصراف'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.pop(context);
                
                setState(() {
                  _isLoading = true;
                });
                
                final provider = Provider.of<ExhibitionProvider>(context, listen: false);
                final exhibitionData = {
                  'name': nameController.text,
                  'description': descriptionController.text.isNotEmpty ? descriptionController.text : null,
                  'address': addressController.text.isNotEmpty ? addressController.text : null,
                  'phone': phoneController.text.isNotEmpty ? phoneController.text : null,
                  'manager': managerController.text.isNotEmpty ? managerController.text : null,
                  'is_active': isActive,
                };
                
                try {
                  if (isEditing) {
                    await provider.updateExhibition(exhibition.id, exhibitionData);
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('نمایشگاه با موفقیت به‌روزرسانی شد')),
                      );
                    }
                  } else {
                    await provider.createExhibition(exhibitionData);
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('نمایشگاه با موفقیت ایجاد شد')),
                      );
                    }
                  }
                  
                  // بارگذاری مجدد نمایشگاه‌ها
                  if (mounted) {
                    await provider.loadExhibitions(refresh: true);
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطا: ${e.toString()}')),
                    );
                  }
                } finally {
                  setState(() {
                    _isLoading = false;
                  });
                }
              }
            },
            child: Text(isEditing ? 'به‌روزرسانی' : 'افزودن'),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmDeleteExhibition(Exhibition exhibition) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف نمایشگاه'),
        content: Text('آیا از حذف نمایشگاه "${exhibition.name}" اطمینان دارید؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('انصراف'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      setState(() {
        _isLoading = true;
      });

      try {
        final provider = Provider.of<ExhibitionProvider>(context, listen: false);
        final success = await provider.deleteExhibition(exhibition.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success ? 'نمایشگاه با موفقیت حذف شد' : 'خطا در حذف نمایشگاه',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
          
          // بارگذاری مجدد نمایشگاه‌ها
          if (success) {
            await provider.loadExhibitions(refresh: true);
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطا: ${e.toString()}')),
          );
        }
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}