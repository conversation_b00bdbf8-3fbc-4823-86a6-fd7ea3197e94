import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/active_exhibition_provider.dart';
import '../models/exhibition.dart';

class ActiveExhibitionWidget extends StatelessWidget {
  const ActiveExhibitionWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<ActiveExhibitionProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Row(
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 12),
                  Text('در حال بارگذاری نمایشگاه فعال...'),
                ],
              ),
            ),
          );
        }

        if (provider.hasError) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      provider.error ?? 'خطا در بارگذاری نمایشگاه فعال',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final exhibition = provider.activeExhibition;
        if (exhibition == null) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.orange),
                  SizedBox(width: 12),
                  Text('هیچ نمایشگاه فعالی انتخاب نشده است'),
                ],
              ),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.event, color: Colors.blue),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        exhibition.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    if (exhibition.isActive)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'فعال',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
                if (exhibition.description.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    exhibition.description,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        exhibition.location,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${exhibition.formattedStartDate} - ${exhibition.formattedEndDate}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class SimpleActiveExhibitionWidget extends StatelessWidget {
  const SimpleActiveExhibitionWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<ActiveExhibitionProvider>(
      builder: (context, provider, child) {
        final exhibition = provider.activeExhibition;
        
        if (exhibition == null) {
          return const Text(
            'هیچ نمایشگاه فعالی انتخاب نشده',
            style: TextStyle(color: Colors.grey),
          );
        }

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.event, size: 16, color: Colors.blue),
            const SizedBox(width: 4),
            Text(
              exhibition.name,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ],
        );
      },
    );
  }
}
