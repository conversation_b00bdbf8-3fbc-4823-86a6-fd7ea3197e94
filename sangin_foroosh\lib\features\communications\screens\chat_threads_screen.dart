import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../providers/messenger_provider.dart';
import '../models/message_thread.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/utils/date_utils.dart';

/// صفحه لیست چت‌ها (سنگین مسنجر)
class ChatThreadsScreen extends StatefulWidget {
  const ChatThreadsScreen({super.key});

  @override
  State<ChatThreadsScreen> createState() => _ChatThreadsScreenState();
}

class _ChatThreadsScreenState extends State<ChatThreadsScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _initializeData();
    _setupScrollListener();
  }
  
  void _initializeData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<MessengerProvider>();
      provider.initializePusher();
      provider.loadThreads(refresh: true);
    });
  }
  
  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= 
          _scrollController.position.maxScrollExtent - 200) {
        context.read<MessengerProvider>().loadMoreThreads();
      }
    });
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'سنگین مسنجر',
          style: TextStyle(
            fontFamily: 'IranSansX',
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.add_comment),
            onPressed: _showNewChatDialog,
          ),
        ],
      ),
      body: Consumer<MessengerProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.threads.isEmpty) {
            return const LoadingWidget(message: 'در حال بارگذاری چت‌ها...');
          }
          
          if (provider.error != null) {
            return CustomErrorWidget(
              message: provider.error!,
              onRetry: () => provider.loadThreads(refresh: true),
            );
          }
          
          if (provider.threads.isEmpty) {
            return _buildEmptyState();
          }
          
          return RefreshIndicator(
            onRefresh: () => provider.loadThreads(refresh: true),
            child: Column(
              children: [
                if (_searchController.text.isNotEmpty) _buildSearchHeader(),
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(8),
                    itemCount: provider.threads.length + 
                        (provider.hasMoreThreads ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index >= provider.threads.length) {
                        return const Padding(
                          padding: EdgeInsets.all(16),
                          child: Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }
                      
                      final thread = provider.threads[index];
                      return _buildThreadItem(thread);
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'هیچ چتی وجود ندارد',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontFamily: 'IranSansX',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'برای شروع گفتگو، روی دکمه + کلیک کنید',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
              fontFamily: 'IranSansX',
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showNewChatDialog,
            icon: const Icon(Icons.add),
            label: const Text(
              'شروع چت جدید',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Row(
        children: [
          Expanded(
            child: Text(
              'نتایج جستجو برای: "${_searchController.text}"',
              style: const TextStyle(
                fontFamily: 'IranSansX',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              _searchController.clear();
              context.read<MessengerProvider>().searchThreads('');
            },
            child: const Text(
              'پاک کردن',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildThreadItem(MessageThread thread) {
    final otherParticipant = thread.participants.firstWhere(
      (p) => p.userId != 'current_user_id', // Replace with actual current user ID
      orElse: () => thread.participants.first,
    );
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        leading: _buildAvatar(otherParticipant),
        title: Text(
          thread.title ?? otherParticipant.name,
          style: TextStyle(
            fontFamily: 'IranSansX',
            fontWeight: thread.unreadCount > 0 ? FontWeight.bold : FontWeight.w500,
            fontSize: 16,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (thread.lastMessage != null) ..[
              const SizedBox(height: 4),
              Text(
                _getLastMessageText(thread.lastMessage!),
                style: TextStyle(
                  fontFamily: 'IranSansX',
                  color: thread.unreadCount > 0 ? Colors.black87 : Colors.grey[600],
                  fontWeight: thread.unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                DateUtils.formatRelativeTime(thread.lastMessage!.timestamp),
                style: TextStyle(
                  fontFamily: 'IranSansX',
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (thread.unreadCount > 0)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  thread.unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'IranSansX',
                  ),
                ),
              ),
            if (thread.lastMessage != null) ..[
              const SizedBox(height: 4),
              _buildMessageStatusIcon(thread.lastMessage!),
            ],
          ],
        ),
        onTap: () => _openChat(thread),
        onLongPress: () => _showThreadOptions(thread),
      ),
    );
  }
  
  Widget _buildAvatar(MessageThreadParticipant participant) {
    return CircleAvatar(
      radius: 28,
      backgroundColor: Colors.grey[300],
      child: participant.avatarUrl != null
          ? ClipOval(
              child: CachedNetworkImage(
                imageUrl: participant.avatarUrl!,
                width: 56,
                height: 56,
                fit: BoxFit.cover,
                placeholder: (context, url) => const CircularProgressIndicator(),
                errorWidget: (context, url, error) => _buildDefaultAvatar(participant.name),
              ),
            )
          : _buildDefaultAvatar(participant.name),
    );
  }
  
  Widget _buildDefaultAvatar(String name) {
    return Text(
      name.isNotEmpty ? name[0].toUpperCase() : '؟',
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.white,
        fontFamily: 'IranSansX',
      ),
    );
  }
  
  Widget _buildMessageStatusIcon(MessagePreview message) {
    IconData icon;
    Color color;
    
    switch (message.status) {
      case 'sent':
        icon = Icons.check;
        color = Colors.grey;
        break;
      case 'delivered':
        icon = Icons.done_all;
        color = Colors.grey;
        break;
      case 'read':
        icon = Icons.done_all;
        color = Theme.of(context).primaryColor;
        break;
      default:
        icon = Icons.schedule;
        color = Colors.grey;
    }
    
    return Icon(
      icon,
      size: 16,
      color: color,
    );
  }
  
  String _getLastMessageText(MessagePreview message) {
    if (message.type == 'image') {
      return '📷 تصویر';
    } else if (message.type == 'file') {
      return '📎 فایل';
    } else if (message.type == 'voice') {
      return '🎵 پیام صوتی';
    } else {
      return message.content;
    }
  }
  
  void _openChat(MessageThread thread) {
    context.read<MessengerProvider>().setCurrentThread(thread);
    context.push('/communications/chat/${thread.id}');
  }
  
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'جستجو در چت‌ها',
          style: TextStyle(fontFamily: 'IranSansX'),
        ),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'نام یا متن پیام را وارد کنید...',
            hintStyle: TextStyle(fontFamily: 'IranSansX'),
            border: OutlineInputBorder(),
          ),
          style: const TextStyle(fontFamily: 'IranSansX'),
          autofocus: true,
          onSubmitted: (value) {
            Navigator.of(context).pop();
            context.read<MessengerProvider>().searchThreads(value);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'لغو',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<MessengerProvider>().searchThreads(_searchController.text);
            },
            child: const Text(
              'جستجو',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
          ),
        ],
      ),
    );
  }
  
  void _showNewChatDialog() {
    final titleController = TextEditingController();
    final participantsController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'چت جدید',
          style: TextStyle(fontFamily: 'IranSansX'),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: titleController,
              decoration: const InputDecoration(
                labelText: 'عنوان چت',
                labelStyle: TextStyle(fontFamily: 'IranSansX'),
                border: OutlineInputBorder(),
              ),
              style: const TextStyle(fontFamily: 'IranSansX'),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: participantsController,
              decoration: const InputDecoration(
                labelText: 'شرکت‌کنندگان (جدا شده با کاما)',
                labelStyle: TextStyle(fontFamily: 'IranSansX'),
                border: OutlineInputBorder(),
                hintText: '<EMAIL>, <EMAIL>',
                hintStyle: TextStyle(fontFamily: 'IranSansX'),
              ),
              style: const TextStyle(fontFamily: 'IranSansX'),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'لغو',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              if (titleController.text.isNotEmpty && 
                  participantsController.text.isNotEmpty) {
                final participants = participantsController.text
                    .split(',')
                    .map((e) => e.trim())
                    .where((e) => e.isNotEmpty)
                    .toList();
                
                final provider = context.read<MessengerProvider>();
                Navigator.of(context).pop();
                
                final success = await provider.createThread(
                  title: titleController.text,
                  participantEmails: participants,
                );
                
                if (success && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'چت جدید با موفقیت ایجاد شد',
                        style: TextStyle(fontFamily: 'IranSansX'),
                      ),
                    ),
                  );
                }
              }
            },
            child: const Text(
              'ایجاد',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
          ),
        ],
      ),
    );
  }
  
  void _showThreadOptions(MessageThread thread) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.mark_email_read),
            title: const Text(
              'علامت‌گذاری به عنوان خوانده شده',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
            onTap: () {
              Navigator.of(context).pop();
              context.read<MessengerProvider>().markThreadAsRead(thread.id);
            },
          ),
          ListTile(
            leading: const Icon(Icons.notifications_off),
            title: const Text(
              'خاموش کردن اعلان‌ها',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
            onTap: () {
              Navigator.of(context).pop();
              // Implement mute functionality
            },
          ),
          ListTile(
            leading: const Icon(Icons.archive),
            title: const Text(
              'بایگانی',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
            onTap: () {
              Navigator.of(context).pop();
              // Implement archive functionality
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text(
              'حذف چت',
              style: TextStyle(
                fontFamily: 'IranSansX',
                color: Colors.red,
              ),
            ),
            onTap: () {
              Navigator.of(context).pop();
              _confirmDeleteThread(thread);
            },
          ),
        ],
      ),
    );
  }
  
  void _confirmDeleteThread(MessageThread thread) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'حذف چت',
          style: TextStyle(fontFamily: 'IranSansX'),
        ),
        content: const Text(
          'آیا مطمئن هستید که می‌خواهید این چت را حذف کنید؟',
          style: TextStyle(fontFamily: 'IranSansX'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'لغو',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Implement delete thread functionality
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'حذف',
              style: TextStyle(fontFamily: 'IranSansX'),
            ),
          ),
        ],
      ),
    );
  }
}