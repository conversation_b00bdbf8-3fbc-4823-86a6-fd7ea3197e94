import '../../../core/services/api_service.dart';
import '../models/machinery.dart';
import '../../../shared/models/machinery_event.dart';

class MachineryRepository {
  final ApiService _apiService;

  MachineryRepository({required ApiService apiService}) : _apiService = apiService;

  Future<Map<String, dynamic>> getMachineries({
    int page = 1,
    int perPage = 10,
    String? search,
    int? categoryId,
    String? status,
  }) async {
    try {
      final response = await _apiService.getMachineries(
        page: page,
        perPage: perPage,
        search: search,
        categoryId: categoryId,
        status: status,
      );
      final List<dynamic> data = response['data'];
      final machineries = data.map((json) => Machinery.fromJson(json)).toList();
      return {
        'machineries': machineries,
        'pagination': response['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Machinery> getMachinery(int id) async {
    try {
      final response = await _apiService.getMachinery(id);
      return Machinery.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Machinery> createMachinery(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.createMachinery(data);
      return Machinery.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<Machinery> updateMachinery(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.updateMachinery(id, data);
      return Machinery.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteMachinery(int id) async {
    try {
      await _apiService.deleteMachinery(id);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  // Machinery Event methods
  Future<Map<String, dynamic>> getMachineryEvents({
    required int machineryId,
    int page = 1,
    int perPage = 10,
  }) async {
    try {
      final response = await _apiService.getMachineryEvents(
        machineryId: machineryId,
        page: page,
        perPage: perPage,
      );
      final List<dynamic> data = response['data'];
      final events = data.map((json) => MachineryEvent.fromJson(json)).toList();
      return {
        'events': events,
        'pagination': response['meta'] ?? {},
      };
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<MachineryEvent> createMachineryEvent(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.createMachineryEvent(data);
      return MachineryEvent.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<MachineryEvent> updateMachineryEvent(int id, Map<String, dynamic> data) async {
    try {
      final response = await _apiService.updateMachineryEvent(id, data);
      return MachineryEvent.fromJson(response['data']);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }

  Future<void> deleteMachineryEvent(int id) async {
    try {
      await _apiService.deleteMachineryEvent(id);
    } catch (e) {
      throw _apiService.handleError(e);
    }
  }
}